"""
Pascal VOC XML format handler.
Primarily supports object detection.
"""

import os
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from PIL import Image

from ..core.detector import TaskType
from ..core.converter import DatasetInfo


class PascalVOCHandler:
    """Handler for Pascal VOC XML format."""
    
    def __init__(self):
        self.supported_tasks = {
            TaskType.OBJECT_DETECTION
        }
    
    def load_dataset(self, dataset_path: str) -> Tuple[Dict, DatasetInfo]:
        """
        Load Pascal VOC dataset from directory.
        
        Args:
            dataset_path: Path to Pascal VOC dataset directory
            
        Returns:
            Tuple of (voc_data, dataset_info)
        """
        try:
            dataset_path = Path(dataset_path)
            
            # Look for Annotations directory
            annotations_dir = dataset_path / "Annotations"
            if not annotations_dir.exists():
                # Try to find XML files in the root directory
                xml_files = list(dataset_path.glob("*.xml"))
                if xml_files:
                    annotations_dir = dataset_path
                else:
                    raise ValueError("No Annotations directory or XML files found")
            
            # Load all XML annotations
            voc_data = self._load_annotations(annotations_dir)
            
            # Look for images
            images_dir = dataset_path / "JPEGImages"
            if not images_dir.exists():
                images_dir = dataset_path / "images"
                if not images_dir.exists():
                    images_dir = dataset_path
            
            voc_data["images_dir"] = str(images_dir)
            
            # Extract dataset information
            dataset_info = self._extract_dataset_info(voc_data)
            
            logger.info(f"Loaded Pascal VOC dataset: {dataset_info}")
            return voc_data, dataset_info
            
        except Exception as e:
            logger.error(f"Failed to load Pascal VOC dataset from {dataset_path}: {e}")
            raise
    
    def save_dataset(self, voc_data: Dict, output_path: str) -> bool:
        """
        Save Pascal VOC dataset to directory.
        
        Args:
            voc_data: Pascal VOC format data
            output_path: Path to save dataset
            
        Returns:
            True if successful, False otherwise
        """
        try:
            output_path = Path(output_path)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Create Annotations directory
            annotations_dir = output_path / "Annotations"
            annotations_dir.mkdir(exist_ok=True)
            
            # Save XML files
            for filename, annotation_data in voc_data["annotations"].items():
                xml_path = annotations_dir / f"{filename}.xml"
                self._save_xml_annotation(annotation_data, xml_path)
            
            logger.info(f"Saved Pascal VOC dataset to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save Pascal VOC dataset to {output_path}: {e}")
            return False
    
    def create_empty_dataset(self, categories: List[str]) -> Dict:
        """
        Create empty Pascal VOC dataset structure.
        
        Args:
            categories: List of category names
            
        Returns:
            Empty Pascal VOC dataset structure
        """
        return {
            "annotations": {},
            "categories": categories,
            "images_dir": ""
        }
    
    def create_annotation(self, image_path: str, image_width: int, image_height: int,
                         objects: List[Dict]) -> Dict:
        """
        Create Pascal VOC annotation structure.
        
        Args:
            image_path: Path to image file
            image_width: Image width
            image_height: Image height
            objects: List of object annotations
            
        Returns:
            Pascal VOC annotation structure
        """
        annotation = {
            "folder": os.path.dirname(image_path),
            "filename": os.path.basename(image_path),
            "path": image_path,
            "source": {
                "database": "Unknown"
            },
            "size": {
                "width": image_width,
                "height": image_height,
                "depth": 3
            },
            "segmented": 0,
            "objects": objects
        }
        
        return annotation
    
    def create_object(self, name: str, bbox: List[float], difficult: int = 0,
                     truncated: int = 0, pose: str = "Unspecified") -> Dict:
        """
        Create Pascal VOC object annotation.
        
        Args:
            name: Object class name
            bbox: Bounding box [xmin, ymin, xmax, ymax]
            difficult: Difficult flag (0 or 1)
            truncated: Truncated flag (0 or 1)
            pose: Object pose
            
        Returns:
            Pascal VOC object structure
        """
        xmin, ymin, xmax, ymax = bbox
        
        return {
            "name": name,
            "pose": pose,
            "truncated": truncated,
            "difficult": difficult,
            "bndbox": {
                "xmin": int(xmin),
                "ymin": int(ymin),
                "xmax": int(xmax),
                "ymax": int(ymax)
            }
        }
    
    def parse_xml_annotation(self, xml_path: str) -> Dict:
        """
        Parse Pascal VOC XML annotation file.
        
        Args:
            xml_path: Path to XML annotation file
            
        Returns:
            Parsed annotation data
        """
        try:
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            annotation = {}
            
            # Basic information
            annotation["folder"] = self._get_text(root, "folder", "")
            annotation["filename"] = self._get_text(root, "filename", "")
            annotation["path"] = self._get_text(root, "path", "")
            
            # Source information
            source = root.find("source")
            if source is not None:
                annotation["source"] = {
                    "database": self._get_text(source, "database", "Unknown")
                }
            else:
                annotation["source"] = {"database": "Unknown"}
            
            # Size information
            size = root.find("size")
            if size is not None:
                annotation["size"] = {
                    "width": int(self._get_text(size, "width", "0")),
                    "height": int(self._get_text(size, "height", "0")),
                    "depth": int(self._get_text(size, "depth", "3"))
                }
            else:
                annotation["size"] = {"width": 0, "height": 0, "depth": 3}
            
            # Segmented flag
            annotation["segmented"] = int(self._get_text(root, "segmented", "0"))
            
            # Objects
            annotation["objects"] = []
            for obj in root.findall("object"):
                obj_data = {
                    "name": self._get_text(obj, "name", ""),
                    "pose": self._get_text(obj, "pose", "Unspecified"),
                    "truncated": int(self._get_text(obj, "truncated", "0")),
                    "difficult": int(self._get_text(obj, "difficult", "0"))
                }
                
                # Bounding box
                bndbox = obj.find("bndbox")
                if bndbox is not None:
                    obj_data["bndbox"] = {
                        "xmin": int(self._get_text(bndbox, "xmin", "0")),
                        "ymin": int(self._get_text(bndbox, "ymin", "0")),
                        "xmax": int(self._get_text(bndbox, "xmax", "0")),
                        "ymax": int(self._get_text(bndbox, "ymax", "0"))
                    }
                
                annotation["objects"].append(obj_data)
            
            return annotation
            
        except Exception as e:
            logger.error(f"Failed to parse XML annotation {xml_path}: {e}")
            raise
    
    def convert_bbox_format(self, bbox: List[float], from_format: str, to_format: str,
                           image_width: int, image_height: int) -> List[float]:
        """
        Convert bounding box between different formats.
        
        Args:
            bbox: Bounding box coordinates
            from_format: Source format ('pascal', 'coco', 'yolo')
            to_format: Target format ('pascal', 'coco', 'yolo')
            image_width: Image width
            image_height: Image height
            
        Returns:
            Converted bounding box coordinates
        """
        if from_format == to_format:
            return bbox
        
        # Convert to Pascal VOC format first [xmin, ymin, xmax, ymax]
        if from_format == 'pascal':
            xmin, ymin, xmax, ymax = bbox
        elif from_format == 'coco':
            # COCO: [x, y, width, height] (top-left corner)
            x, y, w, h = bbox
            xmin, ymin, xmax, ymax = x, y, x + w, y + h
        elif from_format == 'yolo':
            # YOLO: [x_center, y_center, width, height] (normalized)
            x_center, y_center, width, height = bbox
            xmin = (x_center - width / 2) * image_width
            ymin = (y_center - height / 2) * image_height
            xmax = (x_center + width / 2) * image_width
            ymax = (y_center + height / 2) * image_height
        else:
            raise ValueError(f"Unsupported format: {from_format}")
        
        # Convert to target format
        if to_format == 'pascal':
            return [xmin, ymin, xmax, ymax]
        elif to_format == 'coco':
            x = xmin
            y = ymin
            w = xmax - xmin
            h = ymax - ymin
            return [x, y, w, h]
        elif to_format == 'yolo':
            x_center = (xmin + xmax) / 2 / image_width
            y_center = (ymin + ymax) / 2 / image_height
            width = (xmax - xmin) / image_width
            height = (ymax - ymin) / image_height
            return [x_center, y_center, width, height]
        else:
            raise ValueError(f"Unsupported format: {to_format}")
    
    def _load_annotations(self, annotations_dir: Path) -> Dict:
        """Load all XML annotations from directory."""
        voc_data = {
            "annotations": {},
            "categories": set()
        }
        
        for xml_file in annotations_dir.glob("*.xml"):
            try:
                annotation = self.parse_xml_annotation(str(xml_file))
                filename = xml_file.stem
                voc_data["annotations"][filename] = annotation
                
                # Collect categories
                for obj in annotation["objects"]:
                    voc_data["categories"].add(obj["name"])
                    
            except Exception as e:
                logger.warning(f"Failed to load annotation {xml_file}: {e}")
        
        voc_data["categories"] = sorted(list(voc_data["categories"]))
        return voc_data
    
    def _save_xml_annotation(self, annotation_data: Dict, xml_path: Path) -> None:
        """Save annotation data to XML file."""
        root = ET.Element("annotation")
        
        # Basic information
        ET.SubElement(root, "folder").text = annotation_data.get("folder", "")
        ET.SubElement(root, "filename").text = annotation_data.get("filename", "")
        ET.SubElement(root, "path").text = annotation_data.get("path", "")
        
        # Source
        source = ET.SubElement(root, "source")
        source_data = annotation_data.get("source", {})
        ET.SubElement(source, "database").text = source_data.get("database", "Unknown")
        
        # Size
        size = ET.SubElement(root, "size")
        size_data = annotation_data.get("size", {})
        ET.SubElement(size, "width").text = str(size_data.get("width", 0))
        ET.SubElement(size, "height").text = str(size_data.get("height", 0))
        ET.SubElement(size, "depth").text = str(size_data.get("depth", 3))
        
        # Segmented
        ET.SubElement(root, "segmented").text = str(annotation_data.get("segmented", 0))
        
        # Objects
        for obj_data in annotation_data.get("objects", []):
            obj = ET.SubElement(root, "object")
            ET.SubElement(obj, "name").text = obj_data.get("name", "")
            ET.SubElement(obj, "pose").text = obj_data.get("pose", "Unspecified")
            ET.SubElement(obj, "truncated").text = str(obj_data.get("truncated", 0))
            ET.SubElement(obj, "difficult").text = str(obj_data.get("difficult", 0))
            
            # Bounding box
            bndbox_data = obj_data.get("bndbox", {})
            bndbox = ET.SubElement(obj, "bndbox")
            ET.SubElement(bndbox, "xmin").text = str(bndbox_data.get("xmin", 0))
            ET.SubElement(bndbox, "ymin").text = str(bndbox_data.get("ymin", 0))
            ET.SubElement(bndbox, "xmax").text = str(bndbox_data.get("xmax", 0))
            ET.SubElement(bndbox, "ymax").text = str(bndbox_data.get("ymax", 0))
        
        # Write to file
        tree = ET.ElementTree(root)
        ET.indent(tree, space="  ", level=0)
        tree.write(xml_path, encoding="utf-8", xml_declaration=True)
    
    def _extract_dataset_info(self, voc_data: Dict) -> DatasetInfo:
        """Extract dataset information from Pascal VOC data."""
        num_images = len(voc_data["annotations"])
        num_annotations = sum(len(ann["objects"]) for ann in voc_data["annotations"].values())
        categories = voc_data["categories"]
        
        metadata = {
            "images_dir": voc_data.get("images_dir", "")
        }
        
        return DatasetInfo(
            format_type=None,  # Will be set by caller
            task_type=TaskType.OBJECT_DETECTION,
            num_images=num_images,
            num_annotations=num_annotations,
            categories=categories,
            metadata=metadata
        )
    
    def _get_text(self, element, tag: str, default: str = "") -> str:
        """Get text content from XML element."""
        child = element.find(tag)
        return child.text if child is not None and child.text else default
