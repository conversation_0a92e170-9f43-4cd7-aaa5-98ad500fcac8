"""
COCO JSON format handler.
Supports object detection, instance segmentation, and pose estimation.
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
import numpy as np
from PIL import Image

from ..core.detector import TaskType
from ..core.converter import DatasetInfo


class COCOHandler:
    """Handler for COCO JSON format."""
    
    def __init__(self):
        self.supported_tasks = {
            TaskType.OBJECT_DETECTION,
            TaskType.INSTANCE_SEGMENTATION,
            TaskType.POSE_ESTIMATION
        }
    
    def load_dataset(self, annotation_path: str) -> Tuple[Dict, DatasetInfo]:
        """
        Load COCO dataset from annotation file.
        
        Args:
            annotation_path: Path to COCO JSON annotation file
            
        Returns:
            Tuple of (coco_data, dataset_info)
        """
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                coco_data = json.load(f)
            
            # Validate COCO structure
            if not self._validate_coco_structure(coco_data):
                raise ValueError("Invalid COCO format")
            
            # Extract dataset information
            dataset_info = self._extract_dataset_info(coco_data)
            
            logger.info(f"Loaded COCO dataset: {dataset_info}")
            return coco_data, dataset_info
            
        except Exception as e:
            logger.error(f"Failed to load COCO dataset from {annotation_path}: {e}")
            raise
    
    def save_dataset(self, coco_data: Dict, output_path: str) -> bool:
        """
        Save COCO dataset to JSON file.
        
        Args:
            coco_data: COCO format data
            output_path: Path to save JSON file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(coco_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved COCO dataset to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save COCO dataset to {output_path}: {e}")
            return False
    
    def create_empty_dataset(self, task_type: TaskType, categories: List[str]) -> Dict:
        """
        Create empty COCO dataset structure.
        
        Args:
            task_type: Type of computer vision task
            categories: List of category names
            
        Returns:
            Empty COCO dataset structure
        """
        coco_data = {
            "info": {
                "year": datetime.now().year,
                "version": "1.0",
                "description": f"Dataset converted for {task_type.value}",
                "contributor": "Dataset Converter",
                "url": "",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Unknown",
                    "url": ""
                }
            ],
            "categories": [],
            "images": [],
            "annotations": []
        }
        
        # Add categories
        for i, category_name in enumerate(categories):
            category = {
                "id": i,
                "name": category_name,
                "supercategory": "object"
            }
            
            # Add keypoints for pose estimation
            if task_type == TaskType.POSE_ESTIMATION:
                # Default human pose keypoints (COCO format)
                category["keypoints"] = [
                    "nose", "left_eye", "right_eye", "left_ear", "right_ear",
                    "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
                    "left_wrist", "right_wrist", "left_hip", "right_hip",
                    "left_knee", "right_knee", "left_ankle", "right_ankle"
                ]
                category["skeleton"] = [
                    [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
                    [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
                    [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
                    [2, 4], [3, 5], [4, 6], [5, 7]
                ]
            
            coco_data["categories"].append(category)
        
        return coco_data
    
    def add_image(self, coco_data: Dict, image_path: str, image_id: int) -> bool:
        """
        Add image to COCO dataset.
        
        Args:
            coco_data: COCO dataset structure
            image_path: Path to image file
            image_id: Unique image ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get image dimensions
            with Image.open(image_path) as img:
                width, height = img.size
            
            image_info = {
                "id": image_id,
                "file_name": os.path.basename(image_path),
                "width": width,
                "height": height,
                "license": 1,
                "date_captured": datetime.now().isoformat()
            }
            
            coco_data["images"].append(image_info)
            return True
            
        except Exception as e:
            logger.error(f"Failed to add image {image_path}: {e}")
            return False
    
    def add_annotation(self, coco_data: Dict, annotation_data: Dict) -> bool:
        """
        Add annotation to COCO dataset.
        
        Args:
            coco_data: COCO dataset structure
            annotation_data: Annotation data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate required fields
            required_fields = ["id", "image_id", "category_id"]
            if not all(field in annotation_data for field in required_fields):
                logger.error(f"Missing required fields in annotation: {required_fields}")
                return False
            
            # Add task-specific fields
            if "bbox" not in annotation_data and "segmentation" not in annotation_data and "keypoints" not in annotation_data:
                logger.error("Annotation must have bbox, segmentation, or keypoints")
                return False
            
            # Calculate area if not provided
            if "area" not in annotation_data:
                if "bbox" in annotation_data:
                    bbox = annotation_data["bbox"]
                    annotation_data["area"] = bbox[2] * bbox[3]  # width * height
                else:
                    annotation_data["area"] = 0
            
            # Set default values
            annotation_data.setdefault("iscrowd", 0)
            
            coco_data["annotations"].append(annotation_data)
            return True
            
        except Exception as e:
            logger.error(f"Failed to add annotation: {e}")
            return False
    
    def _validate_coco_structure(self, coco_data: Dict) -> bool:
        """Validate COCO dataset structure."""
        required_keys = ["images", "annotations", "categories"]
        return all(key in coco_data for key in required_keys)
    
    def _extract_dataset_info(self, coco_data: Dict) -> DatasetInfo:
        """Extract dataset information from COCO data."""
        num_images = len(coco_data.get("images", []))
        num_annotations = len(coco_data.get("annotations", []))
        categories = [cat["name"] for cat in coco_data.get("categories", [])]
        
        # Detect task type
        task_type = self._detect_task_type(coco_data)
        
        metadata = {
            "info": coco_data.get("info", {}),
            "licenses": coco_data.get("licenses", [])
        }
        
        return DatasetInfo(
            format_type=None,  # Will be set by caller
            task_type=task_type,
            num_images=num_images,
            num_annotations=num_annotations,
            categories=categories,
            metadata=metadata
        )
    
    def _detect_task_type(self, coco_data: Dict) -> TaskType:
        """Detect task type from COCO annotations."""
        if not coco_data.get("annotations"):
            return TaskType.UNKNOWN
        
        sample_annotation = coco_data["annotations"][0]
        
        # Check for keypoints (pose estimation)
        if "keypoints" in sample_annotation and sample_annotation["keypoints"]:
            return TaskType.POSE_ESTIMATION
        
        # Check for segmentation (instance segmentation)
        if "segmentation" in sample_annotation and sample_annotation["segmentation"]:
            return TaskType.INSTANCE_SEGMENTATION
        
        # Check for bbox (object detection)
        if "bbox" in sample_annotation:
            return TaskType.OBJECT_DETECTION
        
        return TaskType.UNKNOWN
    
    def get_image_annotations(self, coco_data: Dict, image_id: int) -> List[Dict]:
        """Get all annotations for a specific image."""
        return [ann for ann in coco_data["annotations"] if ann["image_id"] == image_id]
    
    def get_category_by_id(self, coco_data: Dict, category_id: int) -> Optional[Dict]:
        """Get category information by ID."""
        for category in coco_data["categories"]:
            if category["id"] == category_id:
                return category
        return None
    
    def get_image_by_id(self, coco_data: Dict, image_id: int) -> Optional[Dict]:
        """Get image information by ID."""
        for image in coco_data["images"]:
            if image["id"] == image_id:
                return image
        return None
    
    def convert_bbox_format(self, bbox: List[float], from_format: str, to_format: str, 
                           image_width: int, image_height: int) -> List[float]:
        """
        Convert bounding box between different formats.
        
        Args:
            bbox: Bounding box coordinates
            from_format: Source format ('coco', 'yolo', 'pascal')
            to_format: Target format ('coco', 'yolo', 'pascal')
            image_width: Image width
            image_height: Image height
            
        Returns:
            Converted bounding box coordinates
        """
        if from_format == to_format:
            return bbox
        
        # Convert to normalized center format first
        if from_format == 'coco':
            # COCO: [x, y, width, height] (top-left corner)
            x, y, w, h = bbox
            x_center = (x + w / 2) / image_width
            y_center = (y + h / 2) / image_height
            width = w / image_width
            height = h / image_height
        elif from_format == 'yolo':
            # YOLO: [x_center, y_center, width, height] (normalized)
            x_center, y_center, width, height = bbox
        elif from_format == 'pascal':
            # Pascal VOC: [xmin, ymin, xmax, ymax]
            xmin, ymin, xmax, ymax = bbox
            x_center = (xmin + xmax) / 2 / image_width
            y_center = (ymin + ymax) / 2 / image_height
            width = (xmax - xmin) / image_width
            height = (ymax - ymin) / image_height
        else:
            raise ValueError(f"Unsupported format: {from_format}")
        
        # Convert to target format
        if to_format == 'coco':
            x = x_center * image_width - width * image_width / 2
            y = y_center * image_height - height * image_height / 2
            w = width * image_width
            h = height * image_height
            return [x, y, w, h]
        elif to_format == 'yolo':
            return [x_center, y_center, width, height]
        elif to_format == 'pascal':
            xmin = (x_center - width / 2) * image_width
            ymin = (y_center - height / 2) * image_height
            xmax = (x_center + width / 2) * image_width
            ymax = (y_center + height / 2) * image_height
            return [xmin, ymin, xmax, ymax]
        else:
            raise ValueError(f"Unsupported format: {to_format}")
