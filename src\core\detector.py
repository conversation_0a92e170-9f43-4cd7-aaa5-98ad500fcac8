"""
Dataset format and task detection module.
Automatically identifies dataset format and computer vision task type.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from enum import Enum
import xml.etree.ElementTree as ET
from loguru import logger


class DatasetFormat(Enum):
    """Supported dataset formats."""
    COCO_JSON = "coco_json"
    PASCAL_VOC = "pascal_voc"
    YOLO_TXT = "yolo_txt"
    LABELME_JSON = "labelme_json"
    UNKNOWN = "unknown"


class TaskType(Enum):
    """Supported computer vision tasks."""
    OBJECT_DETECTION = "object_detection"
    INSTANCE_SEGMENTATION = "instance_segmentation"
    POSE_ESTIMATION = "pose_estimation"
    ORIENTED_DETECTION = "oriented_detection"
    UNKNOWN = "unknown"


class DatasetDetector:
    """Automatically detect dataset format and task type."""
    
    def __init__(self):
        self.supported_image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def detect_dataset(self, dataset_path: str) -> Tuple[DatasetFormat, TaskType, Dict]:
        """
        Detect dataset format and task type from a given path.
        
        Args:
            dataset_path: Path to the dataset directory or annotation file
            
        Returns:
            Tuple of (format, task_type, metadata)
        """
        dataset_path = Path(dataset_path)
        
        if not dataset_path.exists():
            logger.error(f"Dataset path does not exist: {dataset_path}")
            return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, {}
        
        # If it's a file, detect based on file content
        if dataset_path.is_file():
            return self._detect_from_file(dataset_path)
        
        # If it's a directory, analyze the structure
        return self._detect_from_directory(dataset_path)
    
    def _detect_from_file(self, file_path: Path) -> Tuple[DatasetFormat, TaskType, Dict]:
        """Detect format from a single annotation file."""
        metadata = {"file_path": str(file_path)}
        
        if file_path.suffix.lower() == '.json':
            return self._detect_json_format(file_path, metadata)
        elif file_path.suffix.lower() == '.xml':
            return self._detect_xml_format(file_path, metadata)
        elif file_path.suffix.lower() == '.txt':
            return self._detect_txt_format(file_path, metadata)
        
        return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, metadata
    
    def _detect_from_directory(self, dir_path: Path) -> Tuple[DatasetFormat, TaskType, Dict]:
        """Detect format from directory structure."""
        metadata = {"directory_path": str(dir_path)}
        
        # Look for common annotation files
        json_files = list(dir_path.glob("**/*.json"))
        xml_files = list(dir_path.glob("**/*.xml"))
        txt_files = list(dir_path.glob("**/*.txt"))
        
        # Check for COCO format (single JSON file with specific structure)
        for json_file in json_files:
            if self._is_coco_json(json_file):
                task_type = self._detect_coco_task_type(json_file)
                metadata.update({"annotation_file": str(json_file)})
                return DatasetFormat.COCO_JSON, task_type, metadata
        
        # Check for LabelMe format (multiple JSON files, one per image)
        if json_files and self._is_labelme_structure(dir_path, json_files):
            task_type = self._detect_labelme_task_type(json_files[0])
            metadata.update({"sample_file": str(json_files[0])})
            return DatasetFormat.LABELME_JSON, task_type, metadata
        
        # Check for Pascal VOC format (XML files)
        if xml_files and self._is_pascal_voc_structure(dir_path, xml_files):
            task_type = self._detect_pascal_voc_task_type(xml_files[0])
            metadata.update({"sample_file": str(xml_files[0])})
            return DatasetFormat.PASCAL_VOC, task_type, metadata
        
        # Check for YOLO format (TXT files with corresponding images)
        if txt_files and self._is_yolo_structure(dir_path, txt_files):
            task_type = self._detect_yolo_task_type(txt_files[0])
            metadata.update({"sample_file": str(txt_files[0])})
            return DatasetFormat.YOLO_TXT, task_type, metadata
        
        return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, metadata
    
    def _detect_json_format(self, file_path: Path, metadata: Dict) -> Tuple[DatasetFormat, TaskType, Dict]:
        """Detect JSON format (COCO or LabelMe)."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check for COCO format
            if self._is_coco_structure(data):
                task_type = self._detect_coco_task_type_from_data(data)
                return DatasetFormat.COCO_JSON, task_type, metadata
            
            # Check for LabelMe format
            if self._is_labelme_structure_data(data):
                task_type = self._detect_labelme_task_type_from_data(data)
                return DatasetFormat.LABELME_JSON, task_type, metadata
                
        except Exception as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
        
        return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, metadata
    
    def _detect_xml_format(self, file_path: Path, metadata: Dict) -> Tuple[DatasetFormat, TaskType, Dict]:
        """Detect XML format (Pascal VOC)."""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            if root.tag == 'annotation' and root.find('object') is not None:
                task_type = self._detect_pascal_voc_task_type_from_xml(root)
                return DatasetFormat.PASCAL_VOC, task_type, metadata
                
        except Exception as e:
            logger.error(f"Error reading XML file {file_path}: {e}")
        
        return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, metadata
    
    def _detect_txt_format(self, file_path: Path, metadata: Dict) -> Tuple[DatasetFormat, TaskType, Dict]:
        """Detect TXT format (YOLO)."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines and self._is_yolo_format_content(lines):
                task_type = self._detect_yolo_task_type_from_content(lines)
                return DatasetFormat.YOLO_TXT, task_type, metadata
                
        except Exception as e:
            logger.error(f"Error reading TXT file {file_path}: {e}")
        
        return DatasetFormat.UNKNOWN, TaskType.UNKNOWN, metadata
    
    def _is_coco_json(self, file_path: Path) -> bool:
        """Check if JSON file is COCO format."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return self._is_coco_structure(data)
        except:
            return False
    
    def _is_coco_structure(self, data: Dict) -> bool:
        """Check if data has COCO structure."""
        required_keys = {'images', 'annotations', 'categories'}
        return all(key in data for key in required_keys)
    
    def _is_labelme_structure(self, dir_path: Path, json_files: List[Path]) -> bool:
        """Check if directory has LabelMe structure."""
        # LabelMe typically has one JSON file per image
        image_files = []
        for ext in self.supported_image_extensions:
            image_files.extend(dir_path.glob(f"**/*{ext}"))
        
        return len(json_files) > 1 and len(image_files) > 0
    
    def _is_labelme_structure_data(self, data: Dict) -> bool:
        """Check if data has LabelMe structure."""
        required_keys = {'shapes', 'imagePath'}
        return all(key in data for key in required_keys)
    
    def _is_pascal_voc_structure(self, dir_path: Path, xml_files: List[Path]) -> bool:
        """Check if directory has Pascal VOC structure."""
        # Pascal VOC typically has XML files in Annotations folder
        return len(xml_files) > 0
    
    def _is_yolo_structure(self, dir_path: Path, txt_files: List[Path]) -> bool:
        """Check if directory has YOLO structure."""
        # YOLO typically has TXT files with same names as images
        image_files = []
        for ext in self.supported_image_extensions:
            image_files.extend(dir_path.glob(f"**/*{ext}"))
        
        return len(txt_files) > 0 and len(image_files) > 0
    
    def _is_yolo_format_content(self, lines: List[str]) -> bool:
        """Check if TXT content is YOLO format."""
        if not lines:
            return False
        
        try:
            # YOLO format: class_id x_center y_center width height [additional_points...]
            parts = lines[0].strip().split()
            if len(parts) >= 5:
                # Check if first element is integer (class_id)
                int(parts[0])
                # Check if coordinates are floats between 0 and 1
                for i in range(1, 5):
                    coord = float(parts[i])
                    if not (0 <= coord <= 1):
                        return False
                return True
        except:
            pass
        
        return False
    
    def _detect_coco_task_type(self, file_path: Path) -> TaskType:
        """Detect task type from COCO JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return self._detect_coco_task_type_from_data(data)
        except:
            return TaskType.UNKNOWN
    
    def _detect_coco_task_type_from_data(self, data: Dict) -> TaskType:
        """Detect task type from COCO data structure."""
        if 'annotations' not in data or not data['annotations']:
            return TaskType.UNKNOWN
        
        sample_annotation = data['annotations'][0]
        
        # Check for keypoints (pose estimation)
        if 'keypoints' in sample_annotation and sample_annotation['keypoints']:
            return TaskType.POSE_ESTIMATION
        
        # Check for segmentation (instance segmentation)
        if 'segmentation' in sample_annotation and sample_annotation['segmentation']:
            return TaskType.INSTANCE_SEGMENTATION
        
        # Check for bbox (object detection)
        if 'bbox' in sample_annotation:
            return TaskType.OBJECT_DETECTION
        
        return TaskType.UNKNOWN
    
    def _detect_labelme_task_type(self, file_path: Path) -> TaskType:
        """Detect task type from LabelMe JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return self._detect_labelme_task_type_from_data(data)
        except:
            return TaskType.UNKNOWN
    
    def _detect_labelme_task_type_from_data(self, data: Dict) -> TaskType:
        """Detect task type from LabelMe data structure."""
        if 'shapes' not in data or not data['shapes']:
            return TaskType.UNKNOWN
        
        shape_types = {shape.get('shape_type', '') for shape in data['shapes']}
        
        if 'polygon' in shape_types:
            return TaskType.INSTANCE_SEGMENTATION
        elif 'rectangle' in shape_types:
            return TaskType.OBJECT_DETECTION
        elif 'point' in shape_types:
            return TaskType.POSE_ESTIMATION
        
        return TaskType.UNKNOWN
    
    def _detect_pascal_voc_task_type(self, file_path: Path) -> TaskType:
        """Detect task type from Pascal VOC XML file."""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            return self._detect_pascal_voc_task_type_from_xml(root)
        except:
            return TaskType.UNKNOWN
    
    def _detect_pascal_voc_task_type_from_xml(self, root) -> TaskType:
        """Detect task type from Pascal VOC XML root."""
        # Pascal VOC primarily supports object detection
        if root.find('object') is not None:
            return TaskType.OBJECT_DETECTION
        
        return TaskType.UNKNOWN
    
    def _detect_yolo_task_type(self, file_path: Path) -> TaskType:
        """Detect task type from YOLO TXT file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            return self._detect_yolo_task_type_from_content(lines)
        except:
            return TaskType.UNKNOWN
    
    def _detect_yolo_task_type_from_content(self, lines: List[str]) -> TaskType:
        """Detect task type from YOLO content."""
        if not lines:
            return TaskType.UNKNOWN
        
        try:
            parts = lines[0].strip().split()
            if len(parts) == 5:
                # Standard object detection: class_id x_center y_center width height
                return TaskType.OBJECT_DETECTION
            elif len(parts) > 5:
                # Could be segmentation (polygon points) or pose estimation (keypoints)
                # For now, assume segmentation if many points
                if len(parts) > 10:
                    return TaskType.INSTANCE_SEGMENTATION
                else:
                    return TaskType.POSE_ESTIMATION
        except:
            pass
        
        return TaskType.UNKNOWN
