"""
Tests for dataset format detection.
"""

import pytest
import tempfile
import json
import os
from pathlib import Path

# Add src to path for testing
import sys
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from src.core.detector import DatasetDetector, DatasetFormat, TaskType


class TestDatasetDetector:
    """Test cases for DatasetDetector."""
    
    def setup_method(self):
        """Setup test environment."""
        self.detector = DatasetDetector()
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """Cleanup test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_detect_coco_json(self):
        """Test COCO JSON format detection."""
        # Create sample COCO JSON
        coco_data = {
            "images": [
                {
                    "id": 1,
                    "file_name": "test.jpg",
                    "width": 640,
                    "height": 480
                }
            ],
            "annotations": [
                {
                    "id": 1,
                    "image_id": 1,
                    "category_id": 1,
                    "bbox": [100, 100, 50, 50],
                    "area": 2500,
                    "iscrowd": 0
                }
            ],
            "categories": [
                {
                    "id": 1,
                    "name": "person",
                    "supercategory": "person"
                }
            ]
        }
        
        # Save to file
        json_path = Path(self.temp_dir) / "annotations.json"
        with open(json_path, 'w') as f:
            json.dump(coco_data, f)
        
        # Test detection
        format_type, task_type, metadata = self.detector.detect_dataset(str(json_path))
        
        assert format_type == DatasetFormat.COCO_JSON
        assert task_type == TaskType.OBJECT_DETECTION
    
    def test_detect_labelme_json(self):
        """Test LabelMe JSON format detection."""
        # Create sample LabelMe JSON
        labelme_data = {
            "version": "5.0.0",
            "flags": {},
            "shapes": [
                {
                    "label": "person",
                    "points": [[100, 100], [200, 200]],
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                }
            ],
            "imagePath": "test.jpg",
            "imageHeight": 480,
            "imageWidth": 640
        }
        
        # Save to file
        json_path = Path(self.temp_dir) / "test.json"
        with open(json_path, 'w') as f:
            json.dump(labelme_data, f)
        
        # Test detection
        format_type, task_type, metadata = self.detector.detect_dataset(str(json_path))
        
        assert format_type == DatasetFormat.LABELME_JSON
        assert task_type == TaskType.OBJECT_DETECTION
    
    def test_detect_yolo_txt(self):
        """Test YOLO TXT format detection."""
        # Create sample YOLO annotation
        yolo_content = "0 0.5 0.5 0.2 0.3\n1 0.3 0.7 0.1 0.15\n"
        
        # Save to file
        txt_path = Path(self.temp_dir) / "test.txt"
        with open(txt_path, 'w') as f:
            f.write(yolo_content)
        
        # Test detection
        format_type, task_type, metadata = self.detector.detect_dataset(str(txt_path))
        
        assert format_type == DatasetFormat.YOLO_TXT
        assert task_type == TaskType.OBJECT_DETECTION
    
    def test_detect_unknown_format(self):
        """Test detection of unknown format."""
        # Create invalid file
        invalid_path = Path(self.temp_dir) / "invalid.txt"
        with open(invalid_path, 'w') as f:
            f.write("This is not a valid annotation format")
        
        # Test detection
        format_type, task_type, metadata = self.detector.detect_dataset(str(invalid_path))
        
        assert format_type == DatasetFormat.UNKNOWN
        assert task_type == TaskType.UNKNOWN
    
    def test_detect_nonexistent_path(self):
        """Test detection with non-existent path."""
        nonexistent_path = "/path/that/does/not/exist"
        
        # Test detection
        format_type, task_type, metadata = self.detector.detect_dataset(nonexistent_path)
        
        assert format_type == DatasetFormat.UNKNOWN
        assert task_type == TaskType.UNKNOWN


if __name__ == "__main__":
    pytest.main([__file__])
