"""
YOLO TXT format handler.
Supports object detection, instance segmentation, and pose estimation.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
import numpy as np
from PIL import Image

from ..core.detector import TaskType
from ..core.converter import DatasetInfo


class YOLOHandler:
    """Handler for YOLO TXT format."""
    
    def __init__(self):
        self.supported_tasks = {
            TaskType.OBJECT_DETECTION,
            TaskType.INSTANCE_SEGMENTATION,
            TaskType.POSE_ESTIMATION,
            TaskType.ORIENTED_DETECTION
        }
    
    def load_dataset(self, dataset_path: str) -> Tuple[Dict, DatasetInfo]:
        """
        Load YOLO dataset from directory.
        
        Args:
            dataset_path: Path to YOLO dataset directory
            
        Returns:
            Tuple of (yolo_data, dataset_info)
        """
        try:
            dataset_path = Path(dataset_path)
            
            # Look for data.yaml or dataset.yaml
            yaml_files = list(dataset_path.glob("*.yaml")) + list(dataset_path.glob("*.yml"))
            config_file = None
            
            for yaml_file in yaml_files:
                if yaml_file.name in ["data.yaml", "dataset.yaml", "data.yml", "dataset.yml"]:
                    config_file = yaml_file
                    break
            
            if not config_file:
                # Create default config based on directory structure
                config_data = self._create_default_config(dataset_path)
            else:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            
            # Load annotations and images
            yolo_data = self._load_annotations_and_images(dataset_path, config_data)
            
            # Extract dataset information
            dataset_info = self._extract_dataset_info(yolo_data, config_data)
            
            logger.info(f"Loaded YOLO dataset: {dataset_info}")
            return yolo_data, dataset_info
            
        except Exception as e:
            logger.error(f"Failed to load YOLO dataset from {dataset_path}: {e}")
            raise
    
    def save_dataset(self, yolo_data: Dict, output_path: str) -> bool:
        """
        Save YOLO dataset to directory.
        
        Args:
            yolo_data: YOLO format data
            output_path: Path to save dataset
            
        Returns:
            True if successful, False otherwise
        """
        try:
            output_path = Path(output_path)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Save configuration file
            config_path = output_path / "data.yaml"
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(yolo_data["config"], f, default_flow_style=False)
            
            # Save annotations
            for split in ["train", "val", "test"]:
                if split in yolo_data["annotations"]:
                    split_dir = output_path / split
                    split_dir.mkdir(exist_ok=True)
                    
                    # Create labels directory
                    labels_dir = split_dir / "labels"
                    labels_dir.mkdir(exist_ok=True)
                    
                    # Save annotation files
                    for image_name, annotations in yolo_data["annotations"][split].items():
                        label_name = Path(image_name).stem + ".txt"
                        label_path = labels_dir / label_name
                        
                        with open(label_path, 'w', encoding='utf-8') as f:
                            for annotation in annotations:
                                f.write(annotation + "\n")
            
            logger.info(f"Saved YOLO dataset to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save YOLO dataset to {output_path}: {e}")
            return False
    
    def create_empty_dataset(self, task_type: TaskType, categories: List[str]) -> Dict:
        """
        Create empty YOLO dataset structure.
        
        Args:
            task_type: Type of computer vision task
            categories: List of category names
            
        Returns:
            Empty YOLO dataset structure
        """
        config = {
            "path": ".",
            "train": "train",
            "val": "val",
            "test": "test",
            "nc": len(categories),
            "names": categories
        }
        
        # Add task-specific configuration
        if task_type == TaskType.POSE_ESTIMATION:
            # Default human pose keypoints (17 keypoints)
            config["kpt_shape"] = [17, 3]  # [num_keypoints, 3 (x, y, visibility)]
        
        return {
            "config": config,
            "annotations": {
                "train": {},
                "val": {},
                "test": {}
            },
            "images": {
                "train": [],
                "val": [],
                "test": []
            }
        }
    
    def add_annotation(self, yolo_data: Dict, split: str, image_name: str, 
                      class_id: int, coordinates: List[float], task_type: TaskType) -> bool:
        """
        Add annotation to YOLO dataset.
        
        Args:
            yolo_data: YOLO dataset structure
            split: Dataset split (train/val/test)
            image_name: Name of the image file
            class_id: Class ID
            coordinates: Annotation coordinates (format depends on task_type)
            task_type: Type of computer vision task
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if split not in yolo_data["annotations"]:
                yolo_data["annotations"][split] = {}
            
            if image_name not in yolo_data["annotations"][split]:
                yolo_data["annotations"][split][image_name] = []
            
            # Format annotation based on task type
            if task_type == TaskType.OBJECT_DETECTION:
                # Format: class_id x_center y_center width height
                if len(coordinates) != 4:
                    raise ValueError("Object detection requires 4 coordinates")
                annotation = f"{class_id} {' '.join(map(str, coordinates))}"
            
            elif task_type == TaskType.INSTANCE_SEGMENTATION:
                # Format: class_id x1 y1 x2 y2 ... xn yn (polygon points)
                if len(coordinates) < 6 or len(coordinates) % 2 != 0:
                    raise ValueError("Instance segmentation requires at least 3 points (6 coordinates)")
                annotation = f"{class_id} {' '.join(map(str, coordinates))}"
            
            elif task_type == TaskType.POSE_ESTIMATION:
                # Format: class_id x_center y_center width height x1 y1 v1 x2 y2 v2 ... (keypoints)
                if len(coordinates) < 4:
                    raise ValueError("Pose estimation requires at least bbox coordinates")
                annotation = f"{class_id} {' '.join(map(str, coordinates))}"
            
            elif task_type == TaskType.ORIENTED_DETECTION:
                # Format: class_id x1 y1 x2 y2 x3 y3 x4 y4 (4 corner points)
                if len(coordinates) != 8:
                    raise ValueError("Oriented detection requires 8 coordinates (4 points)")
                annotation = f"{class_id} {' '.join(map(str, coordinates))}"
            
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
            
            yolo_data["annotations"][split][image_name].append(annotation)
            return True
            
        except Exception as e:
            logger.error(f"Failed to add annotation: {e}")
            return False
    
    def parse_annotation_line(self, line: str, task_type: TaskType) -> Tuple[int, List[float]]:
        """
        Parse YOLO annotation line.
        
        Args:
            line: Annotation line from TXT file
            task_type: Type of computer vision task
            
        Returns:
            Tuple of (class_id, coordinates)
        """
        parts = line.strip().split()
        if not parts:
            raise ValueError("Empty annotation line")
        
        class_id = int(parts[0])
        coordinates = [float(x) for x in parts[1:]]
        
        # Validate coordinates based on task type
        if task_type == TaskType.OBJECT_DETECTION:
            if len(coordinates) != 4:
                raise ValueError(f"Object detection requires 4 coordinates, got {len(coordinates)}")
        elif task_type == TaskType.INSTANCE_SEGMENTATION:
            if len(coordinates) < 6 or len(coordinates) % 2 != 0:
                raise ValueError(f"Instance segmentation requires even number of coordinates >= 6, got {len(coordinates)}")
        elif task_type == TaskType.ORIENTED_DETECTION:
            if len(coordinates) != 8:
                raise ValueError(f"Oriented detection requires 8 coordinates, got {len(coordinates)}")
        
        return class_id, coordinates
    
    def convert_bbox_format(self, bbox: List[float], from_format: str, to_format: str,
                           image_width: int, image_height: int) -> List[float]:
        """
        Convert bounding box between different formats.
        
        Args:
            bbox: Bounding box coordinates
            from_format: Source format ('yolo', 'coco', 'pascal')
            to_format: Target format ('yolo', 'coco', 'pascal')
            image_width: Image width
            image_height: Image height
            
        Returns:
            Converted bounding box coordinates
        """
        if from_format == to_format:
            return bbox
        
        # Convert to normalized center format first (YOLO format)
        if from_format == 'yolo':
            # Already in YOLO format: [x_center, y_center, width, height] (normalized)
            x_center, y_center, width, height = bbox
        elif from_format == 'coco':
            # COCO: [x, y, width, height] (top-left corner, absolute)
            x, y, w, h = bbox
            x_center = (x + w / 2) / image_width
            y_center = (y + h / 2) / image_height
            width = w / image_width
            height = h / image_height
        elif from_format == 'pascal':
            # Pascal VOC: [xmin, ymin, xmax, ymax] (absolute)
            xmin, ymin, xmax, ymax = bbox
            x_center = (xmin + xmax) / 2 / image_width
            y_center = (ymin + ymax) / 2 / image_height
            width = (xmax - xmin) / image_width
            height = (ymax - ymin) / image_height
        else:
            raise ValueError(f"Unsupported format: {from_format}")
        
        # Convert to target format
        if to_format == 'yolo':
            return [x_center, y_center, width, height]
        elif to_format == 'coco':
            x = x_center * image_width - width * image_width / 2
            y = y_center * image_height - height * image_height / 2
            w = width * image_width
            h = height * image_height
            return [x, y, w, h]
        elif to_format == 'pascal':
            xmin = (x_center - width / 2) * image_width
            ymin = (y_center - height / 2) * image_height
            xmax = (x_center + width / 2) * image_width
            ymax = (y_center + height / 2) * image_height
            return [xmin, ymin, xmax, ymax]
        else:
            raise ValueError(f"Unsupported format: {to_format}")
    
    def _create_default_config(self, dataset_path: Path) -> Dict:
        """Create default YOLO configuration based on directory structure."""
        # Look for common directory names
        train_dir = None
        val_dir = None
        test_dir = None
        
        for subdir in dataset_path.iterdir():
            if subdir.is_dir():
                name = subdir.name.lower()
                if name in ["train", "training"]:
                    train_dir = subdir.name
                elif name in ["val", "valid", "validation"]:
                    val_dir = subdir.name
                elif name in ["test", "testing"]:
                    test_dir = subdir.name
        
        # Try to extract class names from label files
        categories = set()
        for txt_file in dataset_path.glob("**/*.txt"):
            try:
                with open(txt_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        parts = line.strip().split()
                        if parts:
                            class_id = int(parts[0])
                            categories.add(class_id)
            except:
                continue
        
        # Create category names
        if categories:
            max_class = max(categories)
            category_names = [f"class_{i}" for i in range(max_class + 1)]
        else:
            category_names = ["class_0"]
        
        return {
            "path": str(dataset_path),
            "train": train_dir or "train",
            "val": val_dir or "val",
            "test": test_dir,
            "nc": len(category_names),
            "names": category_names
        }
    
    def _load_annotations_and_images(self, dataset_path: Path, config_data: Dict) -> Dict:
        """Load annotations and image information from YOLO dataset."""
        yolo_data = {
            "config": config_data,
            "annotations": {},
            "images": {}
        }
        
        # Load each split
        for split in ["train", "val", "test"]:
            if split in config_data and config_data[split]:
                split_path = dataset_path / config_data[split]
                if split_path.exists():
                    yolo_data["annotations"][split] = {}
                    yolo_data["images"][split] = []
                    
                    # Load labels
                    labels_dir = split_path / "labels"
                    if labels_dir.exists():
                        for label_file in labels_dir.glob("*.txt"):
                            with open(label_file, 'r', encoding='utf-8') as f:
                                annotations = [line.strip() for line in f if line.strip()]
                            
                            image_name = label_file.stem
                            yolo_data["annotations"][split][image_name] = annotations
                    
                    # Find corresponding images
                    images_dir = split_path / "images"
                    if not images_dir.exists():
                        images_dir = split_path
                    
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                        for image_file in images_dir.glob(f"*{ext}"):
                            yolo_data["images"][split].append(image_file.name)
        
        return yolo_data
    
    def _extract_dataset_info(self, yolo_data: Dict, config_data: Dict) -> DatasetInfo:
        """Extract dataset information from YOLO data."""
        total_images = 0
        total_annotations = 0
        
        for split in yolo_data["annotations"]:
            total_images += len(yolo_data["images"].get(split, []))
            for annotations in yolo_data["annotations"][split].values():
                total_annotations += len(annotations)
        
        categories = config_data.get("names", [])
        
        # Detect task type based on annotation format
        task_type = self._detect_task_type(yolo_data)
        
        metadata = {
            "config": config_data,
            "splits": list(yolo_data["annotations"].keys())
        }
        
        return DatasetInfo(
            format_type=None,  # Will be set by caller
            task_type=task_type,
            num_images=total_images,
            num_annotations=total_annotations,
            categories=categories,
            metadata=metadata
        )
    
    def _detect_task_type(self, yolo_data: Dict) -> TaskType:
        """Detect task type from YOLO annotations."""
        # Look at first annotation to determine format
        for split in yolo_data["annotations"]:
            for annotations in yolo_data["annotations"][split].values():
                if annotations:
                    parts = annotations[0].strip().split()
                    if len(parts) >= 2:
                        coords_count = len(parts) - 1  # Exclude class_id
                        
                        if coords_count == 4:
                            return TaskType.OBJECT_DETECTION
                        elif coords_count == 8:
                            return TaskType.ORIENTED_DETECTION
                        elif coords_count > 8:
                            # Could be segmentation or pose estimation
                            if coords_count % 2 == 0:
                                return TaskType.INSTANCE_SEGMENTATION
                            else:
                                return TaskType.POSE_ESTIMATION
        
        return TaskType.UNKNOWN
