"""
Image utility functions for dataset operations.
"""

import numpy as np
from typing import <PERSON><PERSON>, List, Optional
from PIL import Image, ImageDraw
from loguru import logger


def get_image_dimensions(image_path: str) -> Tuple[int, int]:
    """
    Get image dimensions (width, height).
    
    Args:
        image_path: Path to image file
        
    Returns:
        Tuple of (width, height)
    """
    try:
        with Image.open(image_path) as img:
            return img.size
    except Exception as e:
        logger.error(f"Failed to get image dimensions for {image_path}: {e}")
        return (0, 0)


def resize_image(image_path: str, output_path: str, size: Tuple[int, int], 
                keep_aspect_ratio: bool = True) -> bool:
    """
    Resize image to specified size.
    
    Args:
        image_path: Path to input image
        output_path: Path to save resized image
        size: Target size (width, height)
        keep_aspect_ratio: Whether to maintain aspect ratio
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            if keep_aspect_ratio:
                img.thumbnail(size, Image.Resampling.LANCZOS)
            else:
                img = img.resize(size, Image.Resampling.LANCZOS)
            
            img.save(output_path)
            return True
            
    except Exception as e:
        logger.error(f"Failed to resize image {image_path}: {e}")
        return False


def crop_image(image_path: str, output_path: str, bbox: Tuple[int, int, int, int]) -> bool:
    """
    Crop image using bounding box.
    
    Args:
        image_path: Path to input image
        output_path: Path to save cropped image
        bbox: Bounding box (left, top, right, bottom)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            cropped = img.crop(bbox)
            cropped.save(output_path)
            return True
            
    except Exception as e:
        logger.error(f"Failed to crop image {image_path}: {e}")
        return False


def draw_bounding_box(image_path: str, output_path: str, bbox: List[float], 
                     label: str = "", color: str = "red", width: int = 2) -> bool:
    """
    Draw bounding box on image.
    
    Args:
        image_path: Path to input image
        output_path: Path to save annotated image
        bbox: Bounding box coordinates [x, y, width, height] or [xmin, ymin, xmax, ymax]
        label: Label text to display
        color: Box color
        width: Line width
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            draw = ImageDraw.Draw(img)
            
            # Convert bbox format if needed
            if len(bbox) == 4:
                x, y, w, h = bbox
                # Assume COCO format [x, y, width, height]
                left, top, right, bottom = x, y, x + w, y + h
            else:
                raise ValueError("Invalid bbox format")
            
            # Draw rectangle
            draw.rectangle([left, top, right, bottom], outline=color, width=width)
            
            # Draw label if provided
            if label:
                draw.text((left, top - 20), label, fill=color)
            
            img.save(output_path)
            return True
            
    except Exception as e:
        logger.error(f"Failed to draw bounding box on {image_path}: {e}")
        return False


def draw_polygon(image_path: str, output_path: str, points: List[List[float]], 
                label: str = "", color: str = "red", width: int = 2) -> bool:
    """
    Draw polygon on image.
    
    Args:
        image_path: Path to input image
        output_path: Path to save annotated image
        points: List of polygon points [[x1, y1], [x2, y2], ...]
        label: Label text to display
        color: Polygon color
        width: Line width
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            draw = ImageDraw.Draw(img)
            
            # Flatten points for PIL
            flat_points = [coord for point in points for coord in point]
            
            # Draw polygon
            draw.polygon(flat_points, outline=color, width=width)
            
            # Draw label if provided
            if label and points:
                x, y = points[0]
                draw.text((x, y - 20), label, fill=color)
            
            img.save(output_path)
            return True
            
    except Exception as e:
        logger.error(f"Failed to draw polygon on {image_path}: {e}")
        return False


def draw_keypoints(image_path: str, output_path: str, keypoints: List[List[float]], 
                  connections: List[List[int]] = None, color: str = "red", 
                  point_radius: int = 3) -> bool:
    """
    Draw keypoints on image.
    
    Args:
        image_path: Path to input image
        output_path: Path to save annotated image
        keypoints: List of keypoints [[x1, y1, v1], [x2, y2, v2], ...]
        connections: List of keypoint connections [[i1, j1], [i2, j2], ...]
        color: Keypoint color
        point_radius: Radius of keypoint circles
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            draw = ImageDraw.Draw(img)
            
            # Draw connections first
            if connections:
                for connection in connections:
                    i, j = connection
                    if i < len(keypoints) and j < len(keypoints):
                        x1, y1 = keypoints[i][:2]
                        x2, y2 = keypoints[j][:2]
                        
                        # Check visibility if available
                        v1 = keypoints[i][2] if len(keypoints[i]) > 2 else 1
                        v2 = keypoints[j][2] if len(keypoints[j]) > 2 else 1
                        
                        if v1 > 0 and v2 > 0:  # Both points visible
                            draw.line([x1, y1, x2, y2], fill=color, width=2)
            
            # Draw keypoints
            for keypoint in keypoints:
                x, y = keypoint[:2]
                visibility = keypoint[2] if len(keypoint) > 2 else 1
                
                if visibility > 0:  # Visible point
                    left = x - point_radius
                    top = y - point_radius
                    right = x + point_radius
                    bottom = y + point_radius
                    draw.ellipse([left, top, right, bottom], fill=color, outline=color)
            
            img.save(output_path)
            return True
            
    except Exception as e:
        logger.error(f"Failed to draw keypoints on {image_path}: {e}")
        return False


def convert_image_format(image_path: str, output_path: str, format: str = "JPEG") -> bool:
    """
    Convert image to different format.
    
    Args:
        image_path: Path to input image
        output_path: Path to save converted image
        format: Target format (JPEG, PNG, BMP, etc.)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if saving as JPEG
            if format.upper() == "JPEG" and img.mode in ("RGBA", "P"):
                img = img.convert("RGB")
            
            img.save(output_path, format=format)
            return True
            
    except Exception as e:
        logger.error(f"Failed to convert image format {image_path}: {e}")
        return False


def calculate_image_statistics(image_paths: List[str]) -> dict:
    """
    Calculate statistics for a list of images.
    
    Args:
        image_paths: List of image file paths
        
    Returns:
        Dictionary with image statistics
    """
    stats = {
        "total_images": len(image_paths),
        "widths": [],
        "heights": [],
        "aspect_ratios": [],
        "file_sizes": [],
        "formats": {}
    }
    
    for image_path in image_paths:
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                stats["widths"].append(width)
                stats["heights"].append(height)
                stats["aspect_ratios"].append(width / height if height > 0 else 0)
                
                # File format
                format_name = img.format or "Unknown"
                stats["formats"][format_name] = stats["formats"].get(format_name, 0) + 1
                
                # File size
                import os
                file_size = os.path.getsize(image_path)
                stats["file_sizes"].append(file_size)
                
        except Exception as e:
            logger.warning(f"Failed to process image {image_path}: {e}")
    
    # Calculate summary statistics
    if stats["widths"]:
        stats["avg_width"] = sum(stats["widths"]) / len(stats["widths"])
        stats["min_width"] = min(stats["widths"])
        stats["max_width"] = max(stats["widths"])
        
        stats["avg_height"] = sum(stats["heights"]) / len(stats["heights"])
        stats["min_height"] = min(stats["heights"])
        stats["max_height"] = max(stats["heights"])
        
        stats["avg_aspect_ratio"] = sum(stats["aspect_ratios"]) / len(stats["aspect_ratios"])
        
        stats["total_file_size"] = sum(stats["file_sizes"])
        stats["avg_file_size"] = stats["total_file_size"] / len(stats["file_sizes"])
    
    return stats


def normalize_coordinates(coords: List[float], image_width: int, image_height: int) -> List[float]:
    """
    Normalize coordinates to [0, 1] range.
    
    Args:
        coords: List of coordinates
        image_width: Image width
        image_height: Image height
        
    Returns:
        Normalized coordinates
    """
    normalized = []
    for i, coord in enumerate(coords):
        if i % 2 == 0:  # x coordinate
            normalized.append(coord / image_width if image_width > 0 else 0)
        else:  # y coordinate
            normalized.append(coord / image_height if image_height > 0 else 0)
    
    return normalized


def denormalize_coordinates(coords: List[float], image_width: int, image_height: int) -> List[float]:
    """
    Denormalize coordinates from [0, 1] range to pixel coordinates.
    
    Args:
        coords: List of normalized coordinates
        image_width: Image width
        image_height: Image height
        
    Returns:
        Pixel coordinates
    """
    denormalized = []
    for i, coord in enumerate(coords):
        if i % 2 == 0:  # x coordinate
            denormalized.append(coord * image_width)
        else:  # y coordinate
            denormalized.append(coord * image_height)
    
    return denormalized


def validate_coordinates(coords: List[float], image_width: int, image_height: int, 
                        normalized: bool = False) -> bool:
    """
    Validate if coordinates are within image bounds.
    
    Args:
        coords: List of coordinates
        image_width: Image width
        image_height: Image height
        normalized: Whether coordinates are normalized
        
    Returns:
        True if valid, False otherwise
    """
    if not coords or len(coords) % 2 != 0:
        return False
    
    max_x = 1.0 if normalized else image_width
    max_y = 1.0 if normalized else image_height
    
    for i in range(0, len(coords), 2):
        x, y = coords[i], coords[i + 1]
        
        if x < 0 or x > max_x or y < 0 or y > max_y:
            return False
    
    return True
