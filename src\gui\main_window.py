"""
Main window for the dataset converter application.
"""

import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QComboBox, QTextEdit, QProgressBar,
    QFileDialog, QMessageBox, QGroupBox, QSplitter, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap
from loguru import logger

from ..core.detector import DatasetDetector, DatasetFormat, TaskType
from ..core.converter import ConversionManager, DatasetInfo


class ConversionWorker(QThread):
    """Worker thread for dataset conversion."""
    
    progress_updated = Signal(int)
    status_updated = Signal(str)
    conversion_finished = Signal(bool, str)
    
    def __init__(self, source_path: str, target_path: str, 
                 source_format: DatasetFormat, target_format: DatasetFormat,
                 task_type: TaskType):
        super().__init__()
        self.source_path = source_path
        self.target_path = target_path
        self.source_format = source_format
        self.target_format = target_format
        self.task_type = task_type
        self.conversion_manager = ConversionManager()
    
    def run(self):
        """Run the conversion process."""
        try:
            self.status_updated.emit("Starting conversion...")
            self.progress_updated.emit(10)
            
            # Perform conversion
            success = self.conversion_manager.convert_dataset(
                self.source_path, self.target_path,
                self.source_format, self.target_format,
                self.task_type
            )
            
            self.progress_updated.emit(100)
            
            if success:
                self.status_updated.emit("Conversion completed successfully!")
                self.conversion_finished.emit(True, "Conversion completed successfully!")
            else:
                self.status_updated.emit("Conversion failed!")
                self.conversion_finished.emit(False, "Conversion failed!")
                
        except Exception as e:
            error_msg = f"Conversion error: {str(e)}"
            self.status_updated.emit(error_msg)
            self.conversion_finished.emit(False, error_msg)


class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.detector = DatasetDetector()
        self.conversion_manager = ConversionManager()
        self.current_dataset_info: Optional[DatasetInfo] = None
        self.conversion_worker: Optional[ConversionWorker] = None
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Dataset Converter - Visual Dataset Format Conversion Tool")
        self.setMinimumSize(1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Input and conversion settings
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Output and information
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([600, 600])
        
        # Status bar
        self.statusBar().showMessage("Ready")
        
        # Progress bar in status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_left_panel(self) -> QWidget:
        """Create the left panel with input controls."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Source dataset group
        source_group = QGroupBox("Source Dataset")
        source_layout = QGridLayout(source_group)
        
        # Source path
        source_layout.addWidget(QLabel("Dataset Path:"), 0, 0)
        self.source_path_edit = QLineEdit()
        self.source_path_edit.setPlaceholderText("Select source dataset directory or file...")
        source_layout.addWidget(self.source_path_edit, 0, 1)
        
        self.browse_source_btn = QPushButton("Browse")
        source_layout.addWidget(self.browse_source_btn, 0, 2)
        
        # Auto-detect button
        self.detect_btn = QPushButton("Auto-Detect Format")
        source_layout.addWidget(self.detect_btn, 1, 0, 1, 3)
        
        # Detected format info
        source_layout.addWidget(QLabel("Detected Format:"), 2, 0)
        self.detected_format_label = QLabel("Not detected")
        self.detected_format_label.setStyleSheet("color: gray;")
        source_layout.addWidget(self.detected_format_label, 2, 1, 1, 2)
        
        source_layout.addWidget(QLabel("Detected Task:"), 3, 0)
        self.detected_task_label = QLabel("Not detected")
        self.detected_task_label.setStyleSheet("color: gray;")
        source_layout.addWidget(self.detected_task_label, 3, 1, 1, 2)
        
        layout.addWidget(source_group)
        
        # Conversion settings group
        conversion_group = QGroupBox("Conversion Settings")
        conversion_layout = QGridLayout(conversion_group)
        
        # Source format override
        conversion_layout.addWidget(QLabel("Source Format:"), 0, 0)
        self.source_format_combo = QComboBox()
        self.source_format_combo.addItems([
            "Auto-detect", "COCO JSON", "Pascal VOC XML", "YOLO TXT", "LabelMe JSON"
        ])
        conversion_layout.addWidget(self.source_format_combo, 0, 1)
        
        # Target format
        conversion_layout.addWidget(QLabel("Target Format:"), 1, 0)
        self.target_format_combo = QComboBox()
        self.target_format_combo.addItems([
            "COCO JSON", "Pascal VOC XML", "YOLO TXT", "LabelMe JSON"
        ])
        conversion_layout.addWidget(self.target_format_combo, 1, 1)
        
        # Task type override
        conversion_layout.addWidget(QLabel("Task Type:"), 2, 0)
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItems([
            "Auto-detect", "Object Detection", "Instance Segmentation", 
            "Pose Estimation", "Oriented Detection"
        ])
        conversion_layout.addWidget(self.task_type_combo, 2, 1)
        
        layout.addWidget(conversion_group)
        
        # Output settings group
        output_group = QGroupBox("Output Settings")
        output_layout = QGridLayout(output_group)
        
        # Output path
        output_layout.addWidget(QLabel("Output Path:"), 0, 0)
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("Select output directory...")
        output_layout.addWidget(self.output_path_edit, 0, 1)
        
        self.browse_output_btn = QPushButton("Browse")
        output_layout.addWidget(self.browse_output_btn, 0, 2)
        
        layout.addWidget(output_group)
        
        # Convert button
        self.convert_btn = QPushButton("Convert Dataset")
        self.convert_btn.setMinimumHeight(50)
        self.convert_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.convert_btn.setEnabled(False)
        layout.addWidget(self.convert_btn)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self) -> QWidget:
        """Create the right panel with output and information."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for different views
        tab_widget = QTabWidget()
        
        # Dataset info tab
        info_tab = self.create_info_tab()
        tab_widget.addTab(info_tab, "Dataset Info")
        
        # Conversion log tab
        log_tab = self.create_log_tab()
        tab_widget.addTab(log_tab, "Conversion Log")
        
        layout.addWidget(tab_widget)
        
        return panel
    
    def create_info_tab(self) -> QWidget:
        """Create the dataset information tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Dataset statistics
        stats_group = QGroupBox("Dataset Statistics")
        stats_layout = QGridLayout(stats_group)
        
        stats_layout.addWidget(QLabel("Format:"), 0, 0)
        self.format_info_label = QLabel("N/A")
        stats_layout.addWidget(self.format_info_label, 0, 1)
        
        stats_layout.addWidget(QLabel("Task Type:"), 1, 0)
        self.task_info_label = QLabel("N/A")
        stats_layout.addWidget(self.task_info_label, 1, 1)
        
        stats_layout.addWidget(QLabel("Number of Images:"), 2, 0)
        self.images_count_label = QLabel("N/A")
        stats_layout.addWidget(self.images_count_label, 2, 1)
        
        stats_layout.addWidget(QLabel("Number of Annotations:"), 3, 0)
        self.annotations_count_label = QLabel("N/A")
        stats_layout.addWidget(self.annotations_count_label, 3, 1)
        
        layout.addWidget(stats_group)
        
        # Categories table
        categories_group = QGroupBox("Categories")
        categories_layout = QVBoxLayout(categories_group)
        
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(2)
        self.categories_table.setHorizontalHeaderLabels(["ID", "Name"])
        self.categories_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        categories_layout.addWidget(self.categories_table)
        
        layout.addWidget(categories_group)
        
        return tab
    
    def create_log_tab(self) -> QWidget:
        """Create the conversion log tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.log_text)
        
        # Clear log button
        clear_btn = QPushButton("Clear Log")
        layout.addWidget(clear_btn)
        clear_btn.clicked.connect(self.log_text.clear)
        
        return tab
    
    def setup_connections(self):
        """Setup signal-slot connections."""
        self.browse_source_btn.clicked.connect(self.browse_source_path)
        self.browse_output_btn.clicked.connect(self.browse_output_path)
        self.detect_btn.clicked.connect(self.detect_dataset_format)
        self.convert_btn.clicked.connect(self.start_conversion)
        
        # Auto-detect when source path changes
        self.source_path_edit.textChanged.connect(self.on_source_path_changed)
        self.output_path_edit.textChanged.connect(self.update_convert_button_state)
    
    def browse_source_path(self):
        """Browse for source dataset path."""
        path = QFileDialog.getExistingDirectory(
            self, "Select Source Dataset Directory"
        )
        if path:
            self.source_path_edit.setText(path)
    
    def browse_output_path(self):
        """Browse for output path."""
        path = QFileDialog.getExistingDirectory(
            self, "Select Output Directory"
        )
        if path:
            self.output_path_edit.setText(path)
    
    def on_source_path_changed(self):
        """Handle source path change."""
        # Enable/disable convert button based on paths and detection
        self.update_convert_button_state()

        # Auto-detect if path exists
        source_path = self.source_path_edit.text().strip()
        if source_path and os.path.exists(source_path):
            # Auto-detect format when path is valid
            self.detect_dataset_format()

    def update_convert_button_state(self):
        """Update convert button enabled state."""
        source_path = self.source_path_edit.text().strip()
        output_path = self.output_path_edit.text().strip()

        # Check if we have valid paths and detected format
        has_paths = bool(source_path and output_path)
        has_format = self.detected_format_label.text() != "Not detected"

        self.convert_btn.setEnabled(has_paths and has_format)
    
    def detect_dataset_format(self):
        """Detect dataset format and task type."""
        source_path = self.source_path_edit.text().strip()
        if not source_path:
            QMessageBox.warning(self, "Warning", "Please select a source dataset path first.")
            return
        
        if not os.path.exists(source_path):
            QMessageBox.warning(self, "Warning", "Source path does not exist.")
            return
        
        try:
            self.log_message("Detecting dataset format...")
            
            # Detect format and task type
            format_type, task_type, metadata = self.detector.detect_dataset(source_path)
            
            # Update UI
            self.detected_format_label.setText(format_type.value.replace('_', ' ').title())
            self.detected_task_label.setText(task_type.value.replace('_', ' ').title())
            
            if format_type != DatasetFormat.UNKNOWN:
                self.detected_format_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.detected_format_label.setStyleSheet("color: red;")
            
            if task_type != TaskType.UNKNOWN:
                self.detected_task_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.detected_task_label.setStyleSheet("color: red;")
            
            self.log_message(f"Detected format: {format_type.value}")
            self.log_message(f"Detected task: {task_type.value}")
            
            # Load dataset info if detection was successful
            if format_type != DatasetFormat.UNKNOWN:
                self.load_dataset_info(source_path, format_type, task_type)

            # Update convert button state after detection
            self.update_convert_button_state()
            
        except Exception as e:
            error_msg = f"Detection failed: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "Error", error_msg)
    
    def load_dataset_info(self, source_path: str, format_type: DatasetFormat, task_type: TaskType):
        """Load and display dataset information."""
        try:
            self.log_message("Loading dataset information...")

            # Import format handlers
            from ..formats.coco import COCOHandler
            from ..formats.pascal_voc import PascalVOCHandler
            from ..formats.yolo import YOLOHandler
            from ..formats.labelme import LabelMeHandler
            from ..utils.file_utils import get_image_files

            num_images = 0
            num_annotations = 0
            categories = []

            # Load actual dataset information based on format
            if format_type == DatasetFormat.COCO_JSON:
                handler = COCOHandler()
                try:
                    _, dataset_info = handler.load_dataset(source_path)
                    num_images = dataset_info.num_images
                    num_annotations = dataset_info.num_annotations
                    categories = dataset_info.categories
                except Exception as e:
                    self.log_message(f"Error loading COCO data: {e}")
                    # Fallback to basic file counting
                    image_files = get_image_files(os.path.dirname(source_path) if os.path.isfile(source_path) else source_path)
                    num_images = len(image_files)

            elif format_type == DatasetFormat.PASCAL_VOC:
                handler = PascalVOCHandler()
                try:
                    _, dataset_info = handler.load_dataset(source_path)
                    num_images = dataset_info.num_images
                    num_annotations = dataset_info.num_annotations
                    categories = dataset_info.categories
                except Exception as e:
                    self.log_message(f"Error loading Pascal VOC data: {e}")
                    # Fallback to basic file counting
                    image_files = get_image_files(source_path)
                    num_images = len(image_files)

            elif format_type == DatasetFormat.YOLO_TXT:
                handler = YOLOHandler()
                try:
                    _, dataset_info = handler.load_dataset(source_path)
                    num_images = dataset_info.num_images
                    num_annotations = dataset_info.num_annotations
                    categories = dataset_info.categories
                except Exception as e:
                    self.log_message(f"Error loading YOLO data: {e}")
                    # Fallback to basic file counting
                    image_files = get_image_files(source_path)
                    num_images = len(image_files)

            elif format_type == DatasetFormat.LABELME_JSON:
                handler = LabelMeHandler()
                try:
                    _, dataset_info = handler.load_dataset(source_path)
                    num_images = dataset_info.num_images
                    num_annotations = dataset_info.num_annotations
                    categories = dataset_info.categories
                except Exception as e:
                    self.log_message(f"Error loading LabelMe data: {e}")
                    # Fallback to basic file counting
                    image_files = get_image_files(source_path)
                    num_images = len(image_files)

            # Create dataset info
            self.current_dataset_info = DatasetInfo(
                format_type=format_type,
                task_type=task_type,
                num_images=num_images,
                num_annotations=num_annotations,
                categories=categories,
                metadata={"source_path": source_path}
            )

            self.update_dataset_info_display()
            self.log_message(f"Dataset info loaded: {num_images} images, {num_annotations} annotations")

        except Exception as e:
            error_msg = f"Failed to load dataset info: {str(e)}"
            self.log_message(error_msg)
            # Create minimal info even if loading fails
            self.current_dataset_info = DatasetInfo(
                format_type=format_type,
                task_type=task_type,
                num_images=0,
                num_annotations=0,
                categories=[],
                metadata={"error": str(e)}
            )
            self.update_dataset_info_display()
    
    def update_dataset_info_display(self):
        """Update the dataset information display."""
        if not self.current_dataset_info:
            return
        
        info = self.current_dataset_info
        
        # Update labels
        self.format_info_label.setText(info.format_type.value.replace('_', ' ').title())
        self.task_info_label.setText(info.task_type.value.replace('_', ' ').title())
        self.images_count_label.setText(str(info.num_images))
        self.annotations_count_label.setText(str(info.num_annotations))
        
        # Update categories table
        self.categories_table.setRowCount(len(info.categories))
        for i, category in enumerate(info.categories):
            self.categories_table.setItem(i, 0, QTableWidgetItem(str(i)))
            self.categories_table.setItem(i, 1, QTableWidgetItem(category))
    
    def start_conversion(self):
        """Start the dataset conversion process."""
        source_path = self.source_path_edit.text().strip()
        output_path = self.output_path_edit.text().strip()
        
        if not source_path or not output_path:
            QMessageBox.warning(self, "Warning", "Please specify both source and output paths.")
            return
        
        # Get format and task type
        source_format = self.get_selected_source_format()
        target_format = self.get_selected_target_format()
        task_type = self.get_selected_task_type()
        
        if source_format == DatasetFormat.UNKNOWN:
            QMessageBox.warning(self, "Warning", "Please detect or specify the source format.")
            return
        
        # Start conversion in worker thread
        self.conversion_worker = ConversionWorker(
            source_path, output_path, source_format, target_format, task_type
        )
        
        # Connect signals
        self.conversion_worker.progress_updated.connect(self.update_progress)
        self.conversion_worker.status_updated.connect(self.log_message)
        self.conversion_worker.conversion_finished.connect(self.on_conversion_finished)
        
        # Update UI
        self.convert_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start conversion
        self.conversion_worker.start()
    
    def get_selected_source_format(self) -> DatasetFormat:
        """Get selected source format."""
        text = self.source_format_combo.currentText()
        if text == "Auto-detect":
            # Use detected format
            detected_text = self.detected_format_label.text()
            if detected_text == "Not detected":
                return DatasetFormat.UNKNOWN
            # Convert display text back to enum
            format_map = {
                "Coco Json": DatasetFormat.COCO_JSON,
                "Pascal Voc": DatasetFormat.PASCAL_VOC,
                "Yolo Txt": DatasetFormat.YOLO_TXT,
                "Labelme Json": DatasetFormat.LABELME_JSON,
                # Additional mappings for different text formats
                "COCO JSON": DatasetFormat.COCO_JSON,
                "Pascal VOC": DatasetFormat.PASCAL_VOC,
                "YOLO TXT": DatasetFormat.YOLO_TXT,
                "LabelMe JSON": DatasetFormat.LABELME_JSON
            }
            return format_map.get(detected_text, DatasetFormat.UNKNOWN)
        else:
            format_map = {
                "COCO JSON": DatasetFormat.COCO_JSON,
                "Pascal VOC XML": DatasetFormat.PASCAL_VOC,
                "YOLO TXT": DatasetFormat.YOLO_TXT,
                "LabelMe JSON": DatasetFormat.LABELME_JSON
            }
            return format_map.get(text, DatasetFormat.UNKNOWN)
    
    def get_selected_target_format(self) -> DatasetFormat:
        """Get selected target format."""
        text = self.target_format_combo.currentText()
        format_map = {
            "COCO JSON": DatasetFormat.COCO_JSON,
            "Pascal VOC XML": DatasetFormat.PASCAL_VOC,
            "YOLO TXT": DatasetFormat.YOLO_TXT,
            "LabelMe JSON": DatasetFormat.LABELME_JSON
        }
        return format_map.get(text, DatasetFormat.UNKNOWN)
    
    def get_selected_task_type(self) -> TaskType:
        """Get selected task type."""
        text = self.task_type_combo.currentText()
        if text == "Auto-detect":
            # Use detected task type
            detected_text = self.detected_task_label.text()
            if detected_text == "Not detected":
                return TaskType.UNKNOWN
            # Convert display text back to enum
            task_map = {
                "Object Detection": TaskType.OBJECT_DETECTION,
                "Instance Segmentation": TaskType.INSTANCE_SEGMENTATION,
                "Pose Estimation": TaskType.POSE_ESTIMATION,
                "Oriented Detection": TaskType.ORIENTED_DETECTION
            }
            return task_map.get(detected_text, TaskType.UNKNOWN)
        else:
            task_map = {
                "Object Detection": TaskType.OBJECT_DETECTION,
                "Instance Segmentation": TaskType.INSTANCE_SEGMENTATION,
                "Pose Estimation": TaskType.POSE_ESTIMATION,
                "Oriented Detection": TaskType.ORIENTED_DETECTION
            }
            return task_map.get(text, TaskType.UNKNOWN)
    
    def update_progress(self, value: int):
        """Update progress bar."""
        self.progress_bar.setValue(value)
    
    def log_message(self, message: str):
        """Add message to log."""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.statusBar().showMessage(message)
    
    def on_conversion_finished(self, success: bool, message: str):
        """Handle conversion completion."""
        self.progress_bar.setVisible(False)
        self.convert_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)
        
        self.conversion_worker = None
