"""
Test script to verify all imports work correctly.
"""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test all module imports."""
    try:
        print("Testing core modules...")
        from src.core.detector import DatasetDetector, DatasetFormat, TaskType
        from src.core.converter import BaseConverter, ConversionManager, DatasetInfo
        print("✓ Core modules imported successfully")
        
        print("Testing format handlers...")
        from src.formats.coco import COCOHandler
        from src.formats.pascal_voc import <PERSON><PERSON><PERSON>andler
        from src.formats.yolo import YOLOHandler
        from src.formats.labelme import LabelMeHandler
        print("✓ Format handlers imported successfully")
        
        print("Testing utility modules...")
        from src.utils.file_utils import get_image_files, ensure_directory
        from src.utils.image_utils import get_image_dimensions, resize_image
        from src.utils.math_utils import calculate_bbox_area, calculate_iou
        print("✓ Utility modules imported successfully")
        
        print("Testing detector functionality...")
        detector = DatasetDetector()
        print(f"✓ DatasetDetector created: {detector}")
        
        print("Testing format handlers...")
        coco_handler = COCOHandler()
        print(f"✓ COCOHandler created: {coco_handler}")
        
        print("\n🎉 All imports successful! Project structure is correct.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_gui_imports():
    """Test GUI imports (may fail if PySide6 not installed)."""
    try:
        print("\nTesting GUI modules...")
        from PySide6.QtWidgets import QApplication
        print("✓ PySide6 is available")
        
        from src.gui.main_window import MainWindow
        print("✓ MainWindow imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"⚠️  GUI import warning: {e}")
        print("   This is expected if PySide6 is not installed")
        return False


if __name__ == "__main__":
    print("Dataset Converter - Import Test")
    print("=" * 40)
    
    success = test_imports()
    gui_success = test_gui_imports()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Core functionality test PASSED")
        if gui_success:
            print("✅ GUI functionality test PASSED")
            print("\n🚀 Ready to run: python main.py")
        else:
            print("⚠️  GUI test failed - install PySide6 to run the full application")
            print("   Run: pip install -r requirements.txt")
    else:
        print("❌ Core functionality test FAILED")
        print("   Please check the project structure and fix import errors")
