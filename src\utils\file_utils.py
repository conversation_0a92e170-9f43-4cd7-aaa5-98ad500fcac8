"""
File utility functions for dataset operations.
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Set
from loguru import logger


def get_image_files(directory: str, extensions: Optional[Set[str]] = None) -> List[Path]:
    """
    Get all image files in a directory.
    
    Args:
        directory: Directory path to search
        extensions: Set of file extensions to include (default: common image formats)
        
    Returns:
        List of image file paths
    """
    if extensions is None:
        extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    
    directory = Path(directory)
    image_files = []
    
    for ext in extensions:
        image_files.extend(directory.glob(f"**/*{ext}"))
        image_files.extend(directory.glob(f"**/*{ext.upper()}"))
    
    return sorted(image_files)


def get_annotation_files(directory: str, format_type: str) -> List[Path]:
    """
    Get annotation files based on format type.
    
    Args:
        directory: Directory path to search
        format_type: Annotation format ('json', 'xml', 'txt')
        
    Returns:
        List of annotation file paths
    """
    directory = Path(directory)
    
    if format_type.lower() == 'json':
        return list(directory.glob("**/*.json"))
    elif format_type.lower() == 'xml':
        return list(directory.glob("**/*.xml"))
    elif format_type.lower() == 'txt':
        return list(directory.glob("**/*.txt"))
    else:
        return []


def ensure_directory(path: str) -> Path:
    """
    Ensure directory exists, create if it doesn't.
    
    Args:
        path: Directory path
        
    Returns:
        Path object
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def copy_file(source: str, destination: str) -> bool:
    """
    Copy file from source to destination.
    
    Args:
        source: Source file path
        destination: Destination file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        source_path = Path(source)
        dest_path = Path(destination)
        
        # Ensure destination directory exists
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(source_path, dest_path)
        return True
        
    except Exception as e:
        logger.error(f"Failed to copy file {source} to {destination}: {e}")
        return False


def move_file(source: str, destination: str) -> bool:
    """
    Move file from source to destination.
    
    Args:
        source: Source file path
        destination: Destination file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        source_path = Path(source)
        dest_path = Path(destination)
        
        # Ensure destination directory exists
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.move(str(source_path), str(dest_path))
        return True
        
    except Exception as e:
        logger.error(f"Failed to move file {source} to {destination}: {e}")
        return False


def get_file_size(file_path: str) -> int:
    """
    Get file size in bytes.
    
    Args:
        file_path: Path to file
        
    Returns:
        File size in bytes, -1 if error
    """
    try:
        return Path(file_path).stat().st_size
    except Exception:
        return -1


def is_valid_image(file_path: str) -> bool:
    """
    Check if file is a valid image.
    
    Args:
        file_path: Path to image file
        
    Returns:
        True if valid image, False otherwise
    """
    try:
        from PIL import Image
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception:
        return False


def get_relative_path(file_path: str, base_path: str) -> str:
    """
    Get relative path from base path.
    
    Args:
        file_path: Full file path
        base_path: Base directory path
        
    Returns:
        Relative path string
    """
    try:
        return str(Path(file_path).relative_to(Path(base_path)))
    except ValueError:
        return str(Path(file_path).name)


def find_corresponding_file(image_path: str, annotation_dir: str, 
                           annotation_ext: str) -> Optional[Path]:
    """
    Find corresponding annotation file for an image.
    
    Args:
        image_path: Path to image file
        annotation_dir: Directory containing annotations
        annotation_ext: Annotation file extension (e.g., '.xml', '.txt')
        
    Returns:
        Path to annotation file if found, None otherwise
    """
    image_path = Path(image_path)
    annotation_dir = Path(annotation_dir)
    
    # Try same name with different extension
    annotation_name = image_path.stem + annotation_ext
    annotation_path = annotation_dir / annotation_name
    
    if annotation_path.exists():
        return annotation_path
    
    # Try recursive search
    for annotation_file in annotation_dir.glob(f"**/{annotation_name}"):
        return annotation_file
    
    return None


def create_directory_structure(base_path: str, structure: dict) -> bool:
    """
    Create directory structure from dictionary.
    
    Args:
        base_path: Base directory path
        structure: Dictionary defining directory structure
        
    Returns:
        True if successful, False otherwise
    """
    try:
        base_path = Path(base_path)
        
        def create_dirs(current_path: Path, struct: dict):
            for name, content in struct.items():
                dir_path = current_path / name
                dir_path.mkdir(exist_ok=True)
                
                if isinstance(content, dict):
                    create_dirs(dir_path, content)
        
        create_dirs(base_path, structure)
        return True
        
    except Exception as e:
        logger.error(f"Failed to create directory structure: {e}")
        return False


def clean_filename(filename: str) -> str:
    """
    Clean filename by removing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Cleaned filename
    """
    import re
    
    # Remove invalid characters
    cleaned = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove multiple underscores
    cleaned = re.sub(r'_+', '_', cleaned)
    
    # Remove leading/trailing underscores
    cleaned = cleaned.strip('_')
    
    return cleaned


def get_unique_filename(directory: str, filename: str) -> str:
    """
    Get unique filename in directory by adding suffix if needed.
    
    Args:
        directory: Directory path
        filename: Desired filename
        
    Returns:
        Unique filename
    """
    directory = Path(directory)
    file_path = directory / filename
    
    if not file_path.exists():
        return filename
    
    # Add suffix to make unique
    stem = Path(filename).stem
    suffix = Path(filename).suffix
    counter = 1
    
    while True:
        new_filename = f"{stem}_{counter}{suffix}"
        new_path = directory / new_filename
        
        if not new_path.exists():
            return new_filename
        
        counter += 1


def validate_dataset_structure(dataset_path: str, required_files: List[str] = None,
                              required_dirs: List[str] = None) -> bool:
    """
    Validate dataset directory structure.
    
    Args:
        dataset_path: Path to dataset directory
        required_files: List of required files
        required_dirs: List of required directories
        
    Returns:
        True if valid structure, False otherwise
    """
    dataset_path = Path(dataset_path)
    
    if not dataset_path.exists() or not dataset_path.is_dir():
        return False
    
    # Check required files
    if required_files:
        for file_name in required_files:
            if not (dataset_path / file_name).exists():
                logger.warning(f"Required file not found: {file_name}")
                return False
    
    # Check required directories
    if required_dirs:
        for dir_name in required_dirs:
            dir_path = dataset_path / dir_name
            if not dir_path.exists() or not dir_path.is_dir():
                logger.warning(f"Required directory not found: {dir_name}")
                return False
    
    return True
