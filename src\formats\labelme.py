"""
LabelMe JSON format handler.
Supports object detection, instance segmentation, and pose estimation.
"""

import json
import os
import base64
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
import numpy as np
from PIL import Image

from ..core.detector import TaskType
from ..core.converter import DatasetInfo


class LabelMeHandler:
    """Handler for LabelMe JSON format."""
    
    def __init__(self):
        self.supported_tasks = {
            TaskType.OBJECT_DETECTION,
            TaskType.INSTANCE_SEGMENTATION,
            TaskType.POSE_ESTIMATION
        }
    
    def load_dataset(self, dataset_path: str) -> Tuple[Dict, DatasetInfo]:
        """
        Load LabelMe dataset from directory.
        
        Args:
            dataset_path: Path to LabelMe dataset directory
            
        Returns:
            Tuple of (labelme_data, dataset_info)
        """
        try:
            dataset_path = Path(dataset_path)
            
            # Find all JSON files
            json_files = list(dataset_path.glob("**/*.json"))
            if not json_files:
                raise ValueError("No JSON files found in dataset directory")
            
            # Load all annotations
            labelme_data = self._load_annotations(json_files)
            
            # Extract dataset information
            dataset_info = self._extract_dataset_info(labelme_data)
            
            logger.info(f"Loaded LabelMe dataset: {dataset_info}")
            return labelme_data, dataset_info
            
        except Exception as e:
            logger.error(f"Failed to load LabelMe dataset from {dataset_path}: {e}")
            raise
    
    def save_dataset(self, labelme_data: Dict, output_path: str) -> bool:
        """
        Save LabelMe dataset to directory.
        
        Args:
            labelme_data: LabelMe format data
            output_path: Path to save dataset
            
        Returns:
            True if successful, False otherwise
        """
        try:
            output_path = Path(output_path)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Save each annotation file
            for filename, annotation_data in labelme_data["annotations"].items():
                json_path = output_path / f"{filename}.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(annotation_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved LabelMe dataset to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save LabelMe dataset to {output_path}: {e}")
            return False
    
    def create_empty_annotation(self, image_path: str, image_width: int, image_height: int) -> Dict:
        """
        Create empty LabelMe annotation structure.
        
        Args:
            image_path: Path to image file
            image_width: Image width
            image_height: Image height
            
        Returns:
            Empty LabelMe annotation structure
        """
        return {
            "version": "5.0.0",
            "flags": {},
            "shapes": [],
            "imagePath": os.path.basename(image_path),
            "imageData": None,  # Can be filled with base64 encoded image data
            "imageHeight": image_height,
            "imageWidth": image_width
        }
    
    def add_shape(self, annotation: Dict, label: str, points: List[List[float]], 
                  shape_type: str, group_id: Optional[int] = None, flags: Dict = None) -> bool:
        """
        Add shape to LabelMe annotation.
        
        Args:
            annotation: LabelMe annotation structure
            label: Object label/class name
            points: List of points [[x1, y1], [x2, y2], ...]
            shape_type: Type of shape (rectangle, polygon, point, etc.)
            group_id: Group ID for related shapes
            flags: Additional flags
            
        Returns:
            True if successful, False otherwise
        """
        try:
            shape = {
                "label": label,
                "points": points,
                "group_id": group_id,
                "shape_type": shape_type,
                "flags": flags or {}
            }
            
            annotation["shapes"].append(shape)
            return True
            
        except Exception as e:
            logger.error(f"Failed to add shape: {e}")
            return False
    
    def create_rectangle_shape(self, label: str, bbox: List[float]) -> Dict:
        """
        Create rectangle shape from bounding box.
        
        Args:
            label: Object label
            bbox: Bounding box [xmin, ymin, xmax, ymax]
            
        Returns:
            Rectangle shape data
        """
        xmin, ymin, xmax, ymax = bbox
        points = [[xmin, ymin], [xmax, ymax]]
        
        return {
            "label": label,
            "points": points,
            "group_id": None,
            "shape_type": "rectangle",
            "flags": {}
        }
    
    def create_polygon_shape(self, label: str, points: List[List[float]]) -> Dict:
        """
        Create polygon shape from points.
        
        Args:
            label: Object label
            points: List of polygon points [[x1, y1], [x2, y2], ...]
            
        Returns:
            Polygon shape data
        """
        return {
            "label": label,
            "points": points,
            "group_id": None,
            "shape_type": "polygon",
            "flags": {}
        }
    
    def create_point_shape(self, label: str, point: List[float]) -> Dict:
        """
        Create point shape.
        
        Args:
            label: Point label
            point: Point coordinates [x, y]
            
        Returns:
            Point shape data
        """
        return {
            "label": label,
            "points": [point],
            "group_id": None,
            "shape_type": "point",
            "flags": {}
        }
    
    def parse_annotation(self, json_path: str) -> Dict:
        """
        Parse LabelMe JSON annotation file.
        
        Args:
            json_path: Path to JSON annotation file
            
        Returns:
            Parsed annotation data
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
            
            # Validate required fields
            required_fields = ["shapes", "imagePath"]
            if not all(field in annotation for field in required_fields):
                logger.warning(f"Missing required fields in {json_path}")
            
            return annotation
            
        except Exception as e:
            logger.error(f"Failed to parse LabelMe annotation {json_path}: {e}")
            raise
    
    def convert_to_bbox(self, points: List[List[float]], shape_type: str) -> List[float]:
        """
        Convert shape points to bounding box.
        
        Args:
            points: Shape points
            shape_type: Type of shape
            
        Returns:
            Bounding box [xmin, ymin, xmax, ymax]
        """
        if shape_type == "rectangle" and len(points) == 2:
            # Rectangle: [[x1, y1], [x2, y2]]
            x1, y1 = points[0]
            x2, y2 = points[1]
            return [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)]
        
        elif shape_type in ["polygon", "line"] and len(points) >= 2:
            # Polygon/Line: [[x1, y1], [x2, y2], ...]
            xs = [p[0] for p in points]
            ys = [p[1] for p in points]
            return [min(xs), min(ys), max(xs), max(ys)]
        
        elif shape_type == "point" and len(points) == 1:
            # Point: [[x, y]]
            x, y = points[0]
            return [x, y, x, y]
        
        else:
            raise ValueError(f"Cannot convert {shape_type} with {len(points)} points to bbox")
    
    def convert_bbox_to_rectangle(self, bbox: List[float]) -> List[List[float]]:
        """
        Convert bounding box to rectangle points.
        
        Args:
            bbox: Bounding box [xmin, ymin, xmax, ymax]
            
        Returns:
            Rectangle points [[x1, y1], [x2, y2]]
        """
        xmin, ymin, xmax, ymax = bbox
        return [[xmin, ymin], [xmax, ymax]]
    
    def encode_image_data(self, image_path: str) -> Optional[str]:
        """
        Encode image as base64 string for embedding in JSON.
        
        Args:
            image_path: Path to image file
            
        Returns:
            Base64 encoded image data or None if failed
        """
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.warning(f"Failed to encode image {image_path}: {e}")
            return None
    
    def decode_image_data(self, image_data: str, output_path: str) -> bool:
        """
        Decode base64 image data and save to file.
        
        Args:
            image_data: Base64 encoded image data
            output_path: Path to save decoded image
            
        Returns:
            True if successful, False otherwise
        """
        try:
            decoded_data = base64.b64decode(image_data)
            with open(output_path, 'wb') as f:
                f.write(decoded_data)
            return True
        except Exception as e:
            logger.error(f"Failed to decode image data: {e}")
            return False
    
    def _load_annotations(self, json_files: List[Path]) -> Dict:
        """Load all JSON annotations from files."""
        labelme_data = {
            "annotations": {},
            "categories": set()
        }
        
        for json_file in json_files:
            try:
                annotation = self.parse_annotation(str(json_file))
                filename = json_file.stem
                labelme_data["annotations"][filename] = annotation
                
                # Collect categories
                for shape in annotation.get("shapes", []):
                    labelme_data["categories"].add(shape.get("label", ""))
                    
            except Exception as e:
                logger.warning(f"Failed to load annotation {json_file}: {e}")
        
        # Remove empty labels
        labelme_data["categories"].discard("")
        labelme_data["categories"] = sorted(list(labelme_data["categories"]))
        return labelme_data
    
    def _extract_dataset_info(self, labelme_data: Dict) -> DatasetInfo:
        """Extract dataset information from LabelMe data."""
        num_images = len(labelme_data["annotations"])
        num_annotations = sum(len(ann.get("shapes", [])) for ann in labelme_data["annotations"].values())
        categories = labelme_data["categories"]
        
        # Detect task type
        task_type = self._detect_task_type(labelme_data)
        
        metadata = {
            "version": "LabelMe format"
        }
        
        return DatasetInfo(
            format_type=None,  # Will be set by caller
            task_type=task_type,
            num_images=num_images,
            num_annotations=num_annotations,
            categories=categories,
            metadata=metadata
        )
    
    def _detect_task_type(self, labelme_data: Dict) -> TaskType:
        """Detect task type from LabelMe annotations."""
        shape_types = set()
        
        for annotation in labelme_data["annotations"].values():
            for shape in annotation.get("shapes", []):
                shape_types.add(shape.get("shape_type", ""))
        
        # Determine task type based on shape types
        if "polygon" in shape_types:
            return TaskType.INSTANCE_SEGMENTATION
        elif "rectangle" in shape_types:
            return TaskType.OBJECT_DETECTION
        elif "point" in shape_types:
            return TaskType.POSE_ESTIMATION
        
        return TaskType.UNKNOWN
