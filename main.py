"""
Dataset Converter - Main Application Entry Point
A visual tool for converting between different dataset formats.
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication
from loguru import logger

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.gui.main_window import MainWindow


def setup_logging():
    """Setup logging configuration."""
    logger.remove()  # Remove default handler

    # Add console handler
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )

    # Add file handler
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    logger.add(
        log_dir / "dataset_converter.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB",
        retention="7 days"
    )


def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    logger.info("Starting Dataset Converter application")

    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Dataset Converter")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Dataset Converter Team")

    # Set application style
    app.setStyle("Fusion")

    # Create and show main window
    window = MainWindow()
    window.show()

    logger.info("Application started successfully")

    # Run application
    try:
        sys.exit(app.exec())
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
