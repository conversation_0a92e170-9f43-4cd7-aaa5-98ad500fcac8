"""
Mathematical utility functions for dataset operations.
"""

import math
import numpy as np
from typing import List, <PERSON><PERSON>, Union


def calculate_bbox_area(bbox: List[float], format_type: str = "coco") -> float:
    """
    Calculate bounding box area.
    
    Args:
        bbox: Bounding box coordinates
        format_type: Format type ('coco', 'pascal', 'yolo')
        
    Returns:
        Area of the bounding box
    """
    if format_type == "coco":
        # COCO: [x, y, width, height]
        _, _, width, height = bbox
        return width * height
    elif format_type == "pascal":
        # Pascal VOC: [xmin, ymin, xmax, ymax]
        xmin, ymin, xmax, ymax = bbox
        return (xmax - xmin) * (ymax - ymin)
    elif format_type == "yolo":
        # YOLO: [x_center, y_center, width, height] (normalized)
        _, _, width, height = bbox
        return width * height  # Already normalized
    else:
        raise ValueError(f"Unsupported format: {format_type}")


def calculate_polygon_area(points: List[List[float]]) -> float:
    """
    Calculate polygon area using shoelace formula.
    
    Args:
        points: List of polygon points [[x1, y1], [x2, y2], ...]
        
    Returns:
        Area of the polygon
    """
    if len(points) < 3:
        return 0.0
    
    area = 0.0
    n = len(points)
    
    for i in range(n):
        j = (i + 1) % n
        area += points[i][0] * points[j][1]
        area -= points[j][0] * points[i][1]
    
    return abs(area) / 2.0


def calculate_iou(bbox1: List[float], bbox2: List[float], format_type: str = "coco") -> float:
    """
    Calculate Intersection over Union (IoU) between two bounding boxes.
    
    Args:
        bbox1: First bounding box
        bbox2: Second bounding box
        format_type: Format type ('coco', 'pascal', 'yolo')
        
    Returns:
        IoU value between 0 and 1
    """
    # Convert to Pascal VOC format [xmin, ymin, xmax, ymax]
    if format_type == "coco":
        # COCO: [x, y, width, height]
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        box1 = [x1, y1, x1 + w1, y1 + h1]
        box2 = [x2, y2, x2 + w2, y2 + h2]
    elif format_type == "pascal":
        box1 = bbox1
        box2 = bbox2
    elif format_type == "yolo":
        # YOLO: [x_center, y_center, width, height] (normalized)
        # Need image dimensions to convert properly
        raise ValueError("YOLO format requires image dimensions for IoU calculation")
    else:
        raise ValueError(f"Unsupported format: {format_type}")
    
    # Calculate intersection
    x1_inter = max(box1[0], box2[0])
    y1_inter = max(box1[1], box2[1])
    x2_inter = min(box1[2], box2[2])
    y2_inter = min(box1[3], box2[3])
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    intersection = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # Calculate union
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0


def point_in_polygon(point: List[float], polygon: List[List[float]]) -> bool:
    """
    Check if a point is inside a polygon using ray casting algorithm.
    
    Args:
        point: Point coordinates [x, y]
        polygon: Polygon points [[x1, y1], [x2, y2], ...]
        
    Returns:
        True if point is inside polygon, False otherwise
    """
    x, y = point
    n = len(polygon)
    inside = False
    
    p1x, p1y = polygon[0]
    for i in range(1, n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return inside


def calculate_distance(point1: List[float], point2: List[float]) -> float:
    """
    Calculate Euclidean distance between two points.
    
    Args:
        point1: First point [x1, y1]
        point2: Second point [x2, y2]
        
    Returns:
        Euclidean distance
    """
    x1, y1 = point1
    x2, y2 = point2
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def rotate_point(point: List[float], center: List[float], angle: float) -> List[float]:
    """
    Rotate a point around a center by given angle.
    
    Args:
        point: Point to rotate [x, y]
        center: Center of rotation [cx, cy]
        angle: Rotation angle in radians
        
    Returns:
        Rotated point [x', y']
    """
    x, y = point
    cx, cy = center
    
    cos_angle = math.cos(angle)
    sin_angle = math.sin(angle)
    
    # Translate to origin
    x -= cx
    y -= cy
    
    # Rotate
    x_new = x * cos_angle - y * sin_angle
    y_new = x * sin_angle + y * cos_angle
    
    # Translate back
    x_new += cx
    y_new += cy
    
    return [x_new, y_new]


def rotate_bbox(bbox: List[float], center: List[float], angle: float) -> List[List[float]]:
    """
    Rotate a bounding box around a center point.
    
    Args:
        bbox: Bounding box [xmin, ymin, xmax, ymax]
        center: Center of rotation [cx, cy]
        angle: Rotation angle in radians
        
    Returns:
        Four corner points of rotated bounding box
    """
    xmin, ymin, xmax, ymax = bbox
    
    # Get four corners
    corners = [
        [xmin, ymin],  # Top-left
        [xmax, ymin],  # Top-right
        [xmax, ymax],  # Bottom-right
        [xmin, ymax]   # Bottom-left
    ]
    
    # Rotate each corner
    rotated_corners = []
    for corner in corners:
        rotated_corner = rotate_point(corner, center, angle)
        rotated_corners.append(rotated_corner)
    
    return rotated_corners


def get_oriented_bbox(points: List[List[float]]) -> List[List[float]]:
    """
    Get oriented bounding box from a set of points.
    
    Args:
        points: List of points [[x1, y1], [x2, y2], ...]
        
    Returns:
        Four corner points of oriented bounding box
    """
    if len(points) < 3:
        return points
    
    # Convert to numpy array for easier computation
    points_array = np.array(points)
    
    # Calculate convex hull
    from scipy.spatial import ConvexHull
    hull = ConvexHull(points_array)
    hull_points = points_array[hull.vertices]
    
    # Find minimum area rectangle
    min_area = float('inf')
    best_rect = None
    
    for i in range(len(hull_points)):
        # Get edge vector
        edge = hull_points[(i + 1) % len(hull_points)] - hull_points[i]
        edge_length = np.linalg.norm(edge)
        
        if edge_length == 0:
            continue
        
        # Normalize edge vector
        edge_unit = edge / edge_length
        
        # Get perpendicular vector
        perp = np.array([-edge_unit[1], edge_unit[0]])
        
        # Project all points onto edge and perpendicular
        edge_projections = np.dot(hull_points, edge_unit)
        perp_projections = np.dot(hull_points, perp)
        
        # Get bounding box in this coordinate system
        min_edge = np.min(edge_projections)
        max_edge = np.max(edge_projections)
        min_perp = np.min(perp_projections)
        max_perp = np.max(perp_projections)
        
        # Calculate area
        width = max_edge - min_edge
        height = max_perp - min_perp
        area = width * height
        
        if area < min_area:
            min_area = area
            
            # Calculate rectangle corners
            corners = np.array([
                [min_edge, min_perp],
                [max_edge, min_perp],
                [max_edge, max_perp],
                [min_edge, max_perp]
            ])
            
            # Transform back to original coordinate system
            transform_matrix = np.column_stack([edge_unit, perp])
            best_rect = np.dot(corners, transform_matrix.T)
    
    return best_rect.tolist() if best_rect is not None else points


def simplify_polygon(points: List[List[float]], tolerance: float = 1.0) -> List[List[float]]:
    """
    Simplify polygon using Douglas-Peucker algorithm.
    
    Args:
        points: Polygon points [[x1, y1], [x2, y2], ...]
        tolerance: Simplification tolerance
        
    Returns:
        Simplified polygon points
    """
    if len(points) <= 2:
        return points
    
    def perpendicular_distance(point: List[float], line_start: List[float], line_end: List[float]) -> float:
        """Calculate perpendicular distance from point to line."""
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # Line length
        line_length = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        
        if line_length == 0:
            return math.sqrt((x0 - x1) ** 2 + (y0 - y1) ** 2)
        
        # Calculate distance
        distance = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1) / line_length
        return distance
    
    def douglas_peucker(points_list: List[List[float]], epsilon: float) -> List[List[float]]:
        """Douglas-Peucker algorithm implementation."""
        if len(points_list) <= 2:
            return points_list
        
        # Find the point with maximum distance
        max_distance = 0
        max_index = 0
        
        for i in range(1, len(points_list) - 1):
            distance = perpendicular_distance(points_list[i], points_list[0], points_list[-1])
            if distance > max_distance:
                max_distance = distance
                max_index = i
        
        # If max distance is greater than epsilon, recursively simplify
        if max_distance > epsilon:
            # Recursive call
            left_part = douglas_peucker(points_list[:max_index + 1], epsilon)
            right_part = douglas_peucker(points_list[max_index:], epsilon)
            
            # Combine results
            return left_part[:-1] + right_part
        else:
            return [points_list[0], points_list[-1]]
    
    return douglas_peucker(points, tolerance)


def calculate_polygon_centroid(points: List[List[float]]) -> List[float]:
    """
    Calculate centroid of a polygon.
    
    Args:
        points: Polygon points [[x1, y1], [x2, y2], ...]
        
    Returns:
        Centroid coordinates [cx, cy]
    """
    if not points:
        return [0, 0]
    
    if len(points) == 1:
        return points[0]
    
    area = calculate_polygon_area(points)
    if area == 0:
        # Fallback to arithmetic mean
        x_sum = sum(p[0] for p in points)
        y_sum = sum(p[1] for p in points)
        return [x_sum / len(points), y_sum / len(points)]
    
    cx = 0
    cy = 0
    n = len(points)
    
    for i in range(n):
        j = (i + 1) % n
        cross = points[i][0] * points[j][1] - points[j][0] * points[i][1]
        cx += (points[i][0] + points[j][0]) * cross
        cy += (points[i][1] + points[j][1]) * cross
    
    factor = 1 / (6 * area)
    return [cx * factor, cy * factor]
