# Dataset Converter

一个基于PySide6的可视化数据集转换工具，支持多种计算机视觉数据集格式之间的相互转换。

## 功能特性

### 支持的数据集格式
- **COCO JSON**: Microsoft COCO数据集格式
- **Pascal VOC XML**: Pascal VOC数据集格式
- **YOLO TXT**: YOLO系列模型使用的文本格式
- **LabelMe JSON**: LabelMe标注工具的JSON格式

### 支持的检测任务
- **目标检测 (Object Detection)**: 矩形边界框检测
- **实例分割 (Instance Segmentation)**: 像素级分割
- **姿态检测 (Pose Estimation)**: 关键点检测
- **定向框检测 (Oriented Detection)**: 旋转边界框检测

### 核心功能
- 🔍 **自动格式识别**: 智能识别数据集格式和任务类型
- 🔄 **格式转换**: 支持各种格式之间的相互转换
- 📊 **数据统计**: 显示数据集详细统计信息
- 🖥️ **可视化界面**: 友好的图形用户界面
- 📝 **转换日志**: 详细的转换过程记录

## 项目结构

```
dataset_converter/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── src/                       # 源代码目录
│   ├── __init__.py
│   ├── gui/                   # GUI界面模块
│   │   ├── __init__.py
│   │   └── main_window.py     # 主窗口
│   ├── core/                  # 核心功能模块
│   │   ├── __init__.py
│   │   ├── detector.py        # 格式和任务自动识别
│   │   └── converter.py       # 转换器基类
│   ├── formats/               # 格式处理模块
│   │   ├── __init__.py
│   │   ├── coco.py           # COCO JSON格式处理
│   │   ├── pascal_voc.py     # Pascal VOC XML格式处理
│   │   ├── yolo.py           # YOLO TXT格式处理
│   │   └── labelme.py        # LabelMe JSON格式处理
│   ├── tasks/                 # 任务处理模块
│   │   └── __init__.py
│   └── utils/                 # 工具函数
│       ├── __init__.py
│       ├── file_utils.py      # 文件操作工具
│       ├── image_utils.py     # 图像处理工具
│       └── math_utils.py      # 数学计算工具
└── tests/                     # 测试文件
    ├── __init__.py
    └── test_detector.py       # 检测器测试
```

## 安装和使用

### 环境要求
- Python 3.9+
- PySide6
- 其他依赖见 requirements.txt

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd dataset_converter
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 运行程序
```bash
python main.py
```

### 使用方法

1. **选择源数据集**: 点击"Browse"按钮选择数据集目录或文件
2. **自动检测格式**: 点击"Auto-Detect Format"自动识别数据集格式和任务类型
3. **设置转换参数**: 选择目标格式和任务类型
4. **选择输出路径**: 指定转换后数据集的保存位置
5. **开始转换**: 点击"Convert Dataset"开始转换过程

## 数据集格式说明

### COCO JSON格式
```json
{
  "images": [...],
  "annotations": [...],
  "categories": [...]
}
```

### Pascal VOC XML格式
```xml
<annotation>
  <object>
    <name>person</name>
    <bndbox>
      <xmin>100</xmin>
      <ymin>100</ymin>
      <xmax>200</xmax>
      <ymax>200</ymax>
    </bndbox>
  </object>
</annotation>
```

### YOLO TXT格式
```
# 目标检测: class_id x_center y_center width height
0 0.5 0.5 0.2 0.3
```

### LabelMe JSON格式
```json
{
  "shapes": [
    {
      "label": "person",
      "points": [[100, 100], [200, 200]],
      "shape_type": "rectangle"
    }
  ],
  "imagePath": "image.jpg"
}
```

## 开发计划

- [x] 项目基础架构
- [x] 格式自动识别
- [x] 基础GUI界面
- [x] 格式处理模块
- [ ] 格式转换器实现
- [ ] 任务特定处理
- [ ] 数据验证功能
- [ ] 批量处理支持
- [ ] 预览功能
- [ ] 配置文件支持

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。